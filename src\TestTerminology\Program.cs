using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Services;
using PPTTranslator.Core.Models;

class Program
{
    static async Task Main(string[] args)
    {
        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<TerminologyManager>();

        // 创建术语库管理器
        var terminologyManager = new TerminologyManager(logger);

        Console.WriteLine("=== 术语库导入导出测试 ===");

        // 测试导入示例术语库
        Console.WriteLine("\n1. 测试导入示例术语库...");
        try
        {
            await terminologyManager.ImportTerminologyAsync("示例术语库.md", "md");
            var entries = terminologyManager.GetAllEntries().ToList();
            Console.WriteLine($"成功导入 {entries.Count} 条术语");
            
            // 显示前几条术语
            Console.WriteLine("前5条术语:");
            foreach (var entry in entries.Take(5))
            {
                Console.WriteLine($"  {entry.SourceTerm} -> {entry.TargetTerm}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导入失败: {ex.Message}");
        }

        // 测试导出Markdown格式
        Console.WriteLine("\n2. 测试导出Markdown格式...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("导出测试.md", "md");
            Console.WriteLine("Markdown导出成功");

            // 读取并显示导出文件的前几行
            if (File.Exists("导出测试.md"))
            {
                var lines = await File.ReadAllLinesAsync("导出测试.md");
                Console.WriteLine("导出文件内容预览:");
                foreach (var line in lines.Take(10))
                {
                    Console.WriteLine($"  {line}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导出失败: {ex.Message}");
        }

        // 测试导出CSV格式
        Console.WriteLine("\n3. 测试导出CSV格式...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("导出测试.csv", "csv");
            Console.WriteLine("CSV导出成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"CSV导出失败: {ex.Message}");
        }

        // 测试导出Excel格式
        Console.WriteLine("\n4. 测试导出Excel格式...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("导出测试.xlsx", "xlsx");
            Console.WriteLine("Excel导出成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Excel导出失败: {ex.Message}");
        }

        Console.WriteLine("\n=== 测试完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
