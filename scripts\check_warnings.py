#!/usr/bin/env python3
"""
代码质量检查脚本
用于检查和修复Python代码中的警告
"""

import os
import sys
import subprocess
import warnings
from pathlib import Path
from typing import List, Dict, Any


def setup_warning_filters():
    """设置警告过滤器，捕获运行时警告"""
    # 捕获所有警告
    warnings.filterwarnings("always")

    # 特定警告的处理 - 忽略第三方库的已知警告
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="gradio")
    warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")
    warnings.filterwarnings("ignore", category=UserWarning, module="pptx")

    # 忽略datetime的弃用警告（来自第三方库）
    warnings.filterwarnings("ignore",
                          message="datetime.datetime.utcfromtimestamp.*is deprecated",
                          category=DeprecationWarning)

    # 忽略importlib-resources的弃用警告
    warnings.filterwarnings("ignore",
                          message="path is deprecated.*Use files.*instead",
                          category=DeprecationWarning)


def check_python_syntax(file_path: str) -> Dict[str, Any]:
    """检查Python文件语法"""
    result = {"file": file_path, "syntax_errors": [], "warnings": []}
    
    try:
        # 编译检查
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        compile(source, file_path, 'exec')
        
    except SyntaxError as e:
        result["syntax_errors"].append({
            "line": e.lineno,
            "message": str(e),
            "text": e.text
        })
    except Exception as e:
        result["warnings"].append({
            "type": "compilation_error",
            "message": str(e)
        })
    
    return result


def run_pylint_check(file_path: str) -> Dict[str, Any]:
    """运行pylint检查"""
    result = {"file": file_path, "issues": []}
    
    try:
        # 运行pylint
        cmd = [
            sys.executable, "-m", "pylint", file_path,
            "--disable=C0114,C0116,R0903,R0913,R0914,R0915,W0613,C0302",
            "--output-format=json"
        ]
        
        process = subprocess.run(
            cmd, capture_output=True, text=True, timeout=30
        )
        
        if process.returncode != 0 and process.stdout:
            import json
            try:
                issues = json.loads(process.stdout)
                result["issues"] = issues
            except json.JSONDecodeError:
                # 如果不是JSON格式，解析文本输出
                result["issues"] = [{"message": process.stdout}]
                
    except subprocess.TimeoutExpired:
        result["issues"] = [{"message": "Pylint检查超时"}]
    except Exception as e:
        result["issues"] = [{"message": f"Pylint检查失败: {str(e)}"}]
    
    return result


def check_import_warnings():
    """检查导入警告"""
    warnings_found = []
    
    try:
        # 测试导入主要模块
        import sys
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            try:
                from src.core.translator import Translator
                from src.ui.gradio_app import TranslatorUI
                from src.utils.config import Config
                
                if w:
                    for warning in w:
                        warnings_found.append({
                            "category": warning.category.__name__,
                            "message": str(warning.message),
                            "filename": warning.filename,
                            "lineno": warning.lineno
                        })
                        
            except ImportError as e:
                warnings_found.append({
                    "category": "ImportError",
                    "message": str(e),
                    "filename": "unknown",
                    "lineno": 0
                })
                
    except Exception as e:
        warnings_found.append({
            "category": "Exception",
            "message": f"导入检查失败: {str(e)}",
            "filename": "unknown",
            "lineno": 0
        })
    
    return warnings_found


def check_runtime_warnings():
    """检查运行时警告"""
    warnings_found = []
    
    try:
        # 设置警告捕获
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 测试配置加载
            try:
                from src.utils.config import Config
                config = Config()
                config.load()
            except Exception as e:
                warnings_found.append({
                    "category": "ConfigError",
                    "message": f"配置加载警告: {str(e)}",
                    "source": "config"
                })
            
            # 记录捕获的警告
            for warning in w:
                warnings_found.append({
                    "category": warning.category.__name__,
                    "message": str(warning.message),
                    "filename": warning.filename,
                    "lineno": warning.lineno,
                    "source": "runtime"
                })
                
    except Exception as e:
        warnings_found.append({
            "category": "Exception",
            "message": f"运行时检查失败: {str(e)}",
            "source": "runtime"
        })
    
    return warnings_found


def main():
    """主函数"""
    print("🔍 开始代码质量检查...")
    
    # 设置警告过滤器
    setup_warning_filters()
    
    # 检查Python文件
    python_files = [
        "main.py",
        "src/core/translator.py",
        "src/ui/gradio_app.py",
        "src/utils/config.py",
        "src/core/terminology.py",
        "src/core/prompt_manager.py",
        "src/api/zhipuai_client.py",
        "src/api/ollama_client.py",
        "src/ui/terminology_manager_ui.py"
    ]
    
    project_root = Path(__file__).parent.parent
    all_issues = []
    
    print("\n📝 检查Python文件语法...")
    for file_path in python_files:
        full_path = project_root / file_path
        if full_path.exists():
            result = check_python_syntax(str(full_path))
            if result["syntax_errors"] or result["warnings"]:
                all_issues.append(result)
                print(f"  ❌ {file_path}: 发现问题")
            else:
                print(f"  ✅ {file_path}: 语法正确")
    
    print("\n🔍 检查导入警告...")
    import_warnings = check_import_warnings()
    if import_warnings:
        print(f"  ⚠️ 发现 {len(import_warnings)} 个导入警告")
        for warning in import_warnings:
            print(f"    - {warning['category']}: {warning['message']}")
    else:
        print("  ✅ 没有导入警告")
    
    print("\n⚡ 检查运行时警告...")
    runtime_warnings = check_runtime_warnings()
    if runtime_warnings:
        print(f"  ⚠️ 发现 {len(runtime_warnings)} 个运行时警告")
        for warning in runtime_warnings:
            print(f"    - {warning['category']}: {warning['message']}")
    else:
        print("  ✅ 没有运行时警告")
    
    # 总结
    total_issues = len(all_issues) + len(import_warnings) + len(runtime_warnings)
    if total_issues == 0:
        print("\n🎉 代码质量检查完成，没有发现问题！")
        return 0
    else:
        print(f"\n⚠️ 代码质量检查完成，发现 {total_issues} 个问题需要关注")
        return 1


if __name__ == "__main__":
    sys.exit(main())
