using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// PPT文档处理接口
/// </summary>
public interface IPPTProcessor
{
    /// <summary>
    /// 提取PPT中的所有文本元素
    /// </summary>
    /// <param name="filePath">PPT文件路径</param>
    /// <returns>文本元素列表</returns>
    Task<List<PPTTextElement>> ExtractTextElementsAsync(string filePath);

    /// <summary>
    /// 翻译PPT文档
    /// </summary>
    /// <param name="inputPath">输入文件路径</param>
    /// <param name="outputPath">输出文件路径</param>
    /// <param name="sourceLanguage">源语言</param>
    /// <param name="targetLanguage">目标语言</param>
    /// <param name="useTerminology">是否使用术语库</param>
    /// <param name="progressCallback">进度回调</param>
    /// <returns>翻译结果</returns>
    Task<PPTTranslationResult> TranslatePPTAsync(
        string inputPath, 
        string outputPath, 
        string sourceLanguage, 
        string targetLanguage, 
        bool useTerminology = true,
        IProgress<TranslationProgress>? progressCallback = null);

    /// <summary>
    /// 替换PPT中的文本
    /// </summary>
    /// <param name="filePath">PPT文件路径</param>
    /// <param name="textElements">要替换的文本元素</param>
    /// <returns>是否成功</returns>
    Task<bool> ReplaceTextInPPTAsync(string filePath, List<PPTTextElement> textElements);

    /// <summary>
    /// 调整字体大小以适应翻译后的文本
    /// </summary>
    /// <param name="filePath">PPT文件路径</param>
    /// <param name="textElements">文本元素</param>
    /// <returns>是否成功</returns>
    Task<bool> AdjustFontSizesAsync(string filePath, List<PPTTextElement> textElements);

    /// <summary>
    /// 创建PPT副本
    /// </summary>
    /// <param name="sourcePath">源文件路径</param>
    /// <param name="destinationPath">目标文件路径</param>
    /// <returns>是否成功</returns>
    Task<bool> CreateCopyAsync(string sourcePath, string destinationPath);

    /// <summary>
    /// 验证PPT文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>验证结果</returns>
    Task<PPTValidationResult> ValidatePPTAsync(string filePath);

    /// <summary>
    /// 获取PPT基本信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>PPT信息</returns>
    Task<PPTInfo> GetPPTInfoAsync(string filePath);
}

/// <summary>
/// PPT翻译结果
/// </summary>
public class PPTTranslationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 翻译的文本元素
    /// </summary>
    public List<PPTTextElement> TranslatedElements { get; set; } = new();

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalSlides { get; set; }

    /// <summary>
    /// 处理的文本数量
    /// </summary>
    public int ProcessedTextCount { get; set; }

    /// <summary>
    /// 翻译耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 翻译统计信息
    /// </summary>
    public TranslationStatistics Statistics { get; set; } = new();
}

/// <summary>
/// PPT验证结果
/// </summary>
public class PPTValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件格式
    /// </summary>
    public string FileFormat { get; set; } = string.Empty;
}

/// <summary>
/// PPT基本信息
/// </summary>
public class PPTInfo
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int SlideCount { get; set; }

    /// <summary>
    /// 文本元素数量
    /// </summary>
    public int TextElementCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// 作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public string Subject { get; set; } = string.Empty;
}

/// <summary>
/// 翻译统计信息
/// </summary>
public class TranslationStatistics
{
    /// <summary>
    /// 总字符数
    /// </summary>
    public int TotalCharacters { get; set; }

    /// <summary>
    /// 总词数
    /// </summary>
    public int TotalWords { get; set; }

    /// <summary>
    /// 使用的术语数量
    /// </summary>
    public int TerminologyUsageCount { get; set; }

    /// <summary>
    /// 平均翻译时间（毫秒/字符）
    /// </summary>
    public double AverageTranslationTimePerCharacter { get; set; }

    /// <summary>
    /// 字体调整次数
    /// </summary>
    public int FontAdjustmentCount { get; set; }
}
