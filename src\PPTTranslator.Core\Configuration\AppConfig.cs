namespace PPTTranslator.Core.Configuration;

/// <summary>
/// 应用程序配置
/// </summary>
public class AppConfig
{
    /// <summary>
    /// 翻译配置
    /// </summary>
    public TranslationConfig Translation { get; set; } = new();

    /// <summary>
    /// 术语库配置
    /// </summary>
    public TerminologyConfig Terminology { get; set; } = new();

    /// <summary>
    /// 字体调整配置
    /// </summary>
    public FontAdjustmentConfig FontAdjustment { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingConfig Logging { get; set; } = new();

    /// <summary>
    /// UI配置
    /// </summary>
    public UIConfig UI { get; set; } = new();
}

/// <summary>
/// 翻译配置
/// </summary>
public class TranslationConfig
{
    /// <summary>
    /// 默认源语言
    /// </summary>
    public string DefaultSourceLanguage { get; set; } = "zh-CN";

    /// <summary>
    /// 默认目标语言
    /// </summary>
    public string DefaultTargetLanguage { get; set; } = "en";

    /// <summary>
    /// 是否默认使用术语库
    /// </summary>
    public bool UseTerminologyByDefault { get; set; } = true;

    /// <summary>
    /// 翻译服务提供商
    /// </summary>
    public string Provider { get; set; } = "ZhipuAI";

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL（用于本地模型）
    /// </summary>
    public string ServerUrl { get; set; } = "http://localhost:11434";

    /// <summary>
    /// 默认模型
    /// </summary>
    public string DefaultModel { get; set; } = "glm-4-flash";

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 批量翻译并发数
    /// </summary>
    public int BatchConcurrency { get; set; } = 5;

    /// <summary>
    /// 是否启用翻译缓存
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 缓存过期时间（小时）
    /// </summary>
    public int CacheExpirationHours { get; set; } = 24;
}

/// <summary>
/// 术语库配置
/// </summary>
public class TerminologyConfig
{
    /// <summary>
    /// 默认术语库文件路径
    /// </summary>
    public string DefaultTerminologyPath { get; set; } = "Data/terminology.json";

    /// <summary>
    /// 术语库目录
    /// </summary>
    public string TerminologyDirectory { get; set; } = "Data/Terminologies";

    /// <summary>
    /// 是否启用术语预处理
    /// </summary>
    public bool EnablePreprocessing { get; set; } = true;

    /// <summary>
    /// 术语匹配模式（Exact, Fuzzy, Smart）
    /// </summary>
    public string MatchingMode { get; set; } = "Smart";

    /// <summary>
    /// 模糊匹配阈值（0-1）
    /// </summary>
    public double FuzzyMatchThreshold { get; set; } = 0.8;

    /// <summary>
    /// 是否区分大小写
    /// </summary>
    public bool CaseSensitive { get; set; } = false;

    /// <summary>
    /// 自动备份术语库
    /// </summary>
    public bool AutoBackup { get; set; } = true;

    /// <summary>
    /// 备份保留天数
    /// </summary>
    public int BackupRetentionDays { get; set; } = 30;
}

/// <summary>
/// 字体调整配置
/// </summary>
public class FontAdjustmentConfig
{
    /// <summary>
    /// 是否启用自动字体调整
    /// </summary>
    public bool EnableAutoAdjustment { get; set; } = true;

    /// <summary>
    /// 最小字体大小
    /// </summary>
    public double MinFontSize { get; set; } = 8.0;

    /// <summary>
    /// 最大字体大小
    /// </summary>
    public double MaxFontSize { get; set; } = 72.0;

    /// <summary>
    /// 最大缩放比例
    /// </summary>
    public double MaxScaleDown { get; set; } = 0.7;

    /// <summary>
    /// 最大放大比例
    /// </summary>
    public double MaxScaleUp { get; set; } = 1.2;

    /// <summary>
    /// 调整阈值
    /// </summary>
    public double AdjustmentThreshold { get; set; } = 0.2;

    /// <summary>
    /// 是否考虑字符宽度差异
    /// </summary>
    public bool ConsiderCharacterWidth { get; set; } = true;

    /// <summary>
    /// 调整算法（Simple, Advanced, AI）
    /// </summary>
    public string AdjustmentAlgorithm { get; set; } = "Advanced";
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingConfig
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string LogFilePath { get; set; } = "Logs/app.log";

    /// <summary>
    /// 是否启用控制台日志
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// 是否启用文件日志
    /// </summary>
    public bool EnableFileLogging { get; set; } = true;

    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 10;

    /// <summary>
    /// 保留的日志文件数量
    /// </summary>
    public int RetainedFileCount { get; set; } = 5;
}

/// <summary>
/// UI配置
/// </summary>
public class UIConfig
{
    /// <summary>
    /// 默认主题
    /// </summary>
    public string DefaultTheme { get; set; } = "Light";

    /// <summary>
    /// 默认语言
    /// </summary>
    public string DefaultLanguage { get; set; } = "zh-CN";

    /// <summary>
    /// 窗口宽度
    /// </summary>
    public double WindowWidth { get; set; } = 1200;

    /// <summary>
    /// 窗口高度
    /// </summary>
    public double WindowHeight { get; set; } = 800;

    /// <summary>
    /// 是否记住窗口位置
    /// </summary>
    public bool RememberWindowPosition { get; set; } = true;

    /// <summary>
    /// 是否显示进度详情
    /// </summary>
    public bool ShowProgressDetails { get; set; } = true;

    /// <summary>
    /// 自动保存间隔（分钟）
    /// </summary>
    public int AutoSaveIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// 最近文件数量
    /// </summary>
    public int RecentFilesCount { get; set; } = 10;
}
