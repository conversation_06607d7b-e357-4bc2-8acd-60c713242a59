using Microsoft.Extensions.Logging;
using Moq;
using PPTTranslator.Core.Models;
using PPTTranslator.Core.Services;
using Xunit;

namespace PPTTranslator.Tests.Services;

/// <summary>
/// 术语库管理器测试
/// </summary>
public class TerminologyManagerTests
{
    private readonly Mock<ILogger<TerminologyManager>> _mockLogger;
    private readonly TerminologyManager _terminologyManager;

    public TerminologyManagerTests()
    {
        _mockLogger = new Mock<ILogger<TerminologyManager>>();
        _terminologyManager = new TerminologyManager(_mockLogger.Object);
    }

    [Fact]
    public void AddTerminologyEntry_ShouldAddNewEntry()
    {
        // Arrange
        var entry = new TerminologyEntry
        {
            SourceTerm = "测试",
            TargetTerm = "Test",
            Category = "技术",
            Description = "测试术语"
        };

        // Act
        _terminologyManager.AddTerminologyEntry(entry);

        // Assert
        var entries = _terminologyManager.GetAllEntries();
        Assert.Contains(entries, e => e.SourceTerm == "测试" && e.TargetTerm == "Test");
    }

    [Fact]
    public void RemoveTerminologyEntry_ShouldRemoveExistingEntry()
    {
        // Arrange
        var entry = new TerminologyEntry
        {
            SourceTerm = "删除测试",
            TargetTerm = "Delete Test"
        };
        _terminologyManager.AddTerminologyEntry(entry);

        // Act
        var result = _terminologyManager.RemoveTerminologyEntry("删除测试");

        // Assert
        Assert.True(result);
        var entries = _terminologyManager.GetAllEntries();
        Assert.DoesNotContain(entries, e => e.SourceTerm == "删除测试");
    }

    [Fact]
    public void PreprocessText_ShouldReplaceTermsWithPlaceholders()
    {
        // Arrange
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "人工智能",
            TargetTerm = "Artificial Intelligence"
        });

        var text = "人工智能是未来的发展方向";

        // Act
        var result = _terminologyManager.PreprocessText(text, "zh-CN", "en");

        // Assert
        Assert.NotEqual(text, result.ProcessedText);
        Assert.Single(result.Matches);
        Assert.Equal("人工智能", result.Matches[0].MatchedText);
        Assert.Equal("Artificial Intelligence", result.Matches[0].ReplacementText);
    }

    [Fact]
    public void PostprocessText_ShouldRestoreTermsFromPlaceholders()
    {
        // Arrange
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "机器学习",
            TargetTerm = "Machine Learning"
        });

        var originalText = "机器学习技术发展迅速";
        var preprocessResult = _terminologyManager.PreprocessText(originalText, "zh-CN", "en");
        var translatedText = "Machine Learning technology is developing rapidly";

        // Act
        var finalText = _terminologyManager.PostprocessText(translatedText, preprocessResult);

        // Assert
        Assert.Contains("Machine Learning", finalText);
    }

    [Theory]
    [InlineData("", "")]
    [InlineData(null, "")]
    [InlineData("没有术语的文本", "没有术语的文本")]
    public void PreprocessText_WithNoTerms_ShouldReturnOriginalText(string input, string expected)
    {
        // Act
        var result = _terminologyManager.PreprocessText(input, "zh-CN", "en");

        // Assert
        Assert.Equal(expected, result.ProcessedText);
        Assert.Empty(result.Matches);
    }

    [Fact]
    public void SearchEntries_ShouldReturnMatchingEntries()
    {
        // Arrange
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "人工智能",
            TargetTerm = "Artificial Intelligence",
            Category = "技术"
        });
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "机器学习",
            TargetTerm = "Machine Learning",
            Category = "技术"
        });

        // Act
        var results = _terminologyManager.SearchEntries("智能");

        // Assert
        Assert.Single(results);
        Assert.Equal("人工智能", results.First().SourceTerm);
    }

    [Fact]
    public void ClearTerminology_ShouldRemoveAllEntries()
    {
        // Arrange
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "测试1",
            TargetTerm = "Test1"
        });
        _terminologyManager.AddTerminologyEntry(new TerminologyEntry
        {
            SourceTerm = "测试2",
            TargetTerm = "Test2"
        });

        // Act
        _terminologyManager.ClearTerminology();

        // Assert
        var entries = _terminologyManager.GetAllEntries();
        Assert.Empty(entries);
    }

    [Fact]
    public void UpdateTerminologyEntry_ShouldUpdateExistingEntry()
    {
        // Arrange
        var originalEntry = new TerminologyEntry
        {
            SourceTerm = "更新测试",
            TargetTerm = "Update Test",
            Category = "原始类别"
        };
        _terminologyManager.AddTerminologyEntry(originalEntry);

        var updatedEntry = new TerminologyEntry
        {
            SourceTerm = "更新测试",
            TargetTerm = "Updated Test",
            Category = "新类别"
        };

        // Act
        var result = _terminologyManager.UpdateTerminologyEntry(updatedEntry);

        // Assert
        Assert.True(result);
        var entries = _terminologyManager.GetAllEntries();
        var entry = entries.First(e => e.SourceTerm == "更新测试");
        Assert.Equal("Updated Test", entry.TargetTerm);
        Assert.Equal("新类别", entry.Category);
    }
}
