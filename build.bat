@echo off
chcp 65001 >nul
echo ========================================
echo PPT翻译工具 - 快速构建脚本
echo ========================================
echo.

REM 检查 PowerShell 是否可用
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo 错误: 需要 PowerShell 支持
    echo 请确保系统已安装 PowerShell
    pause
    exit /b 1
)

REM 检查 .NET SDK
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 .NET SDK
    echo 请先安装 .NET 8.0 SDK
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo 检测到 .NET SDK 版本:
dotnet --version
echo.

REM 询问构建选项
echo 请选择构建选项:
echo 1. 快速构建 (Debug, 不运行测试)
echo 2. 标准构建 (Release, 运行测试)
echo 3. 完整构建 (Release, 运行测试, 创建安装包)
echo 4. 自定义构建
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo 执行快速构建...
    powershell -ExecutionPolicy Bypass -File "%~dp0build.ps1" -Configuration Debug -RunTests:$false
) else if "%choice%"=="2" (
    echo 执行标准构建...
    powershell -ExecutionPolicy Bypass -File "%~dp0build.ps1" -Configuration Release -RunTests:$true
) else if "%choice%"=="3" (
    echo 执行完整构建...
    powershell -ExecutionPolicy Bypass -File "%~dp0build.ps1" -Configuration Release -RunTests:$true -CreatePackage
) else if "%choice%"=="4" (
    echo.
    echo 自定义构建选项:
    echo.
    
    set /p config="配置 (Debug/Release) [Release]: "
    if "%config%"=="" set config=Release
    
    set /p runtime="运行时 (win-x64/win-x86/win-arm64) [win-x64]: "
    if "%runtime%"=="" set runtime=win-x64
    
    set /p tests="运行测试 (y/n) [y]: "
    if "%tests%"=="" set tests=y
    
    set /p package="创建安装包 (y/n) [n]: "
    if "%package%"=="" set package=n
    
    set runTestsFlag=$true
    if /i "%tests%"=="n" set runTestsFlag=$false
    
    set createPackageFlag=
    if /i "%package%"=="y" set createPackageFlag=-CreatePackage
    
    echo.
    echo 执行自定义构建...
    echo 配置: %config%
    echo 运行时: %runtime%
    echo 运行测试: %tests%
    echo 创建安装包: %package%
    echo.
    
    powershell -ExecutionPolicy Bypass -File "%~dp0build.ps1" -Configuration %config% -Runtime %runtime% -RunTests:%runTestsFlag% %createPackageFlag%
) else (
    echo 无效选择，执行默认构建...
    powershell -ExecutionPolicy Bypass -File "%~dp0build.ps1"
)

echo.
if errorlevel 1 (
    echo ========================================
    echo 构建失败!
    echo ========================================
    pause
    exit /b 1
) else (
    echo ========================================
    echo 构建成功!
    echo ========================================
    echo.
    echo 输出目录: %~dp0publish
    echo.
    set /p open="是否打开输出目录? (y/n): "
    if /i "%open%"=="y" (
        explorer "%~dp0publish"
    )
)

pause
