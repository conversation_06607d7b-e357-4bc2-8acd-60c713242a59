using System.Diagnostics;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 文本预处理服务
/// </summary>
public class TextPreprocessor : ITextPreprocessor
{
    private readonly ITerminologyManager _terminologyManager;
    private readonly ILogger<TextPreprocessor> _logger;

    // 中文字符正则表达式
    private static readonly Regex ChineseRegex = new(@"[\u4e00-\u9fff]", RegexOptions.Compiled);
    
    // 英文字符正则表达式
    private static readonly Regex EnglishRegex = new(@"[a-zA-Z]", RegexOptions.Compiled);
    
    // 清理文本的正则表达式
    private static readonly Regex CleanupRegex = new(@"\s+", RegexOptions.Compiled);

    public TextPreprocessor(ITerminologyManager terminologyManager, ILogger<TextPreprocessor> logger)
    {
        _terminologyManager = terminologyManager;
        _logger = logger;
    }

    public TextPreprocessResult PreprocessText(string text, string sourceLanguage, string targetLanguage, bool useTerminology = true)
    {
        var stopwatch = Stopwatch.StartNew();
        
        var result = new TextPreprocessResult
        {
            OriginalText = text ?? string.Empty,
            ProcessedText = text ?? string.Empty,
            DetectedLanguage = DetectLanguage(text ?? string.Empty)
        };

        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                result.ProcessedText = string.Empty;
                return result;
            }

            // 1. 清理文本
            var cleanedText = CleanText(text);
            result.ProcessedText = cleanedText;

            // 2. 检查是否需要分割长文本
            if (cleanedText.Length > 1000)
            {
                result.RequiresSplitting = true;
                result.TextSegments = SplitLongText(cleanedText).ToList();
            }

            // 3. 应用术语库预处理
            if (useTerminology)
            {
                result.TerminologyResult = _terminologyManager.PreprocessText(
                    result.ProcessedText, sourceLanguage, targetLanguage);
                result.ProcessedText = result.TerminologyResult.ProcessedText;
            }

            stopwatch.Stop();
            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;

            _logger.LogDebug("文本预处理完成，耗时: {ElapsedMs}ms, 原始长度: {OriginalLength}, 处理后长度: {ProcessedLength}",
                result.ElapsedMilliseconds, text.Length, result.ProcessedText.Length);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文本预处理失败: {Text}", text);
            throw;
        }
    }

    public string PostprocessText(string translatedText, TextPreprocessResult preprocessResult)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(translatedText))
            {
                return translatedText;
            }

            var result = translatedText;

            // 应用术语库后处理
            if (preprocessResult.TerminologyResult != null)
            {
                result = _terminologyManager.PostprocessText(result, preprocessResult.TerminologyResult);
            }

            // 最终清理
            result = CleanText(result);

            _logger.LogDebug("文本后处理完成，长度: {Length}", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文本后处理失败: {Text}", translatedText);
            return translatedText; // 返回原始翻译结果
        }
    }

    public string CleanText(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return string.Empty;
        }

        // 移除多余的空白字符
        var cleaned = CleanupRegex.Replace(text.Trim(), " ");
        
        // 移除首尾空格
        cleaned = cleaned.Trim();

        // 处理标点符号周围的空格
        cleaned = ProcessPunctuation(cleaned);

        return cleaned;
    }

    public string DetectLanguage(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return "unknown";
        }

        var chineseMatches = ChineseRegex.Matches(text).Count;
        var englishMatches = EnglishRegex.Matches(text).Count;
        var totalChars = text.Length;

        // 检测其他语言
        var koreanMatches = Regex.Matches(text, @"[\uAC00-\uD7AF]").Count; // 韩文
        var japaneseMatches = Regex.Matches(text, @"[\u3040-\u309F\u30A0-\u30FF]").Count; // 日文
        var russianMatches = Regex.Matches(text, @"[\u0400-\u04FF]").Count; // 俄文

        // 计算各语言字符的比例
        var chineseRatio = (double)chineseMatches / totalChars;
        var englishRatio = (double)englishMatches / totalChars;
        var koreanRatio = (double)koreanMatches / totalChars;
        var japaneseRatio = (double)japaneseMatches / totalChars;
        var russianRatio = (double)russianMatches / totalChars;

        // 按优先级检测语言
        if (koreanRatio > 0.3)
        {
            return "ko";
        }
        else if (japaneseRatio > 0.3)
        {
            return "ja";
        }
        else if (russianRatio > 0.3)
        {
            return "ru";
        }
        else if (chineseRatio > 0.3)
        {
            return "zh-CN";
        }
        else if (englishRatio > 0.5)
        {
            return "en";
        }
        else if (koreanMatches > 0)
        {
            return "ko";
        }
        else if (japaneseMatches > 0)
        {
            return "ja";
        }
        else if (russianMatches > 0)
        {
            return "ru";
        }
        else if (chineseMatches > 0)
        {
            return "zh-CN"; // 包含中文字符，倾向于判断为中文
        }
        else if (englishMatches > 0)
        {
            return "en"; // 包含英文字符，倾向于判断为英文
        }

        return "unknown";
    }

    public IEnumerable<string> SplitLongText(string text, int maxLength = 1000)
    {
        if (string.IsNullOrWhiteSpace(text) || text.Length <= maxLength)
        {
            yield return text;
            yield break;
        }

        var sentences = SplitIntoSentences(text);
        var currentSegment = string.Empty;

        foreach (var sentence in sentences)
        {
            // 如果单个句子就超过最大长度，强制分割
            if (sentence.Length > maxLength)
            {
                if (!string.IsNullOrEmpty(currentSegment))
                {
                    yield return currentSegment.Trim();
                    currentSegment = string.Empty;
                }

                // 按字符强制分割长句子
                for (int i = 0; i < sentence.Length; i += maxLength)
                {
                    var length = Math.Min(maxLength, sentence.Length - i);
                    yield return sentence.Substring(i, length);
                }
            }
            else if (currentSegment.Length + sentence.Length + 1 > maxLength)
            {
                // 当前段落加上新句子会超过最大长度，输出当前段落
                if (!string.IsNullOrEmpty(currentSegment))
                {
                    yield return currentSegment.Trim();
                }
                currentSegment = sentence;
            }
            else
            {
                // 添加到当前段落
                if (string.IsNullOrEmpty(currentSegment))
                {
                    currentSegment = sentence;
                }
                else
                {
                    currentSegment += " " + sentence;
                }
            }
        }

        // 输出最后一个段落
        if (!string.IsNullOrEmpty(currentSegment))
        {
            yield return currentSegment.Trim();
        }
    }

    private string ProcessPunctuation(string text)
    {
        // 处理中文标点符号
        text = Regex.Replace(text, @"\s*([，。！？；：])\s*", "$1");
        
        // 处理英文标点符号
        text = Regex.Replace(text, @"\s*([,.!?;:])\s*", "$1 ");
        
        // 处理括号
        text = Regex.Replace(text, @"\s*([（(])\s*", " $1");
        text = Regex.Replace(text, @"\s*([）)])\s*", "$1 ");
        
        // 处理引号
        text = Regex.Replace(text, @"\s*([""\u201c\u201d])\s*", "$1");
        
        return text.Trim();
    }

    private IEnumerable<string> SplitIntoSentences(string text)
    {
        // 中文句子分割符
        var chineseSentenceEnders = new[] { "。", "！", "？", "；" };
        
        // 英文句子分割符
        var englishSentenceEnders = new[] { ".", "!", "?", ";" };
        
        var sentences = new List<string>();
        var currentSentence = string.Empty;

        for (int i = 0; i < text.Length; i++)
        {
            var currentChar = text[i].ToString();
            currentSentence += currentChar;

            // 检查是否是句子结束符
            if (chineseSentenceEnders.Contains(currentChar) || englishSentenceEnders.Contains(currentChar))
            {
                // 检查下一个字符是否是空格或句子结束（避免小数点等误判）
                if (i == text.Length - 1 || char.IsWhiteSpace(text[i + 1]) || 
                    chineseSentenceEnders.Contains(text[i + 1].ToString()) ||
                    englishSentenceEnders.Contains(text[i + 1].ToString()))
                {
                    sentences.Add(currentSentence.Trim());
                    currentSentence = string.Empty;
                }
            }
        }

        // 添加最后一个句子
        if (!string.IsNullOrEmpty(currentSentence))
        {
            sentences.Add(currentSentence.Trim());
        }

        return sentences.Where(s => !string.IsNullOrWhiteSpace(s));
    }
}
