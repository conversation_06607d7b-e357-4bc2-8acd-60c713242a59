using System.Diagnostics;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Presentation;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;
using A = DocumentFormat.OpenXml.Drawing;
using P = DocumentFormat.OpenXml.Presentation;

namespace PPTTranslator.Core.Services;

/// <summary>
/// PPT文档处理服务
/// </summary>
public class PPTProcessor : IPPTProcessor
{
    private readonly ILogger<PPTProcessor> _logger;
    private readonly ITranslationService _translationService;
    private readonly ITextPreprocessor _textPreprocessor;

    public PPTProcessor(
        ILogger<PPTProcessor> logger,
        ITranslationService translationService,
        ITextPreprocessor textPreprocessor)
    {
        _logger = logger;
        _translationService = translationService;
        _textPreprocessor = textPreprocessor;
    }

    public Task<List<PPTTextElement>> ExtractTextElementsAsync(string filePath)
    {
        var textElements = new List<PPTTextElement>();

        try
        {
            using var presentationDocument = PresentationDocument.Open(filePath, false);
            var presentationPart = presentationDocument.PresentationPart;
            
            if (presentationPart?.Presentation?.SlideIdList == null)
            {
                _logger.LogWarning("PPT文档中没有找到幻灯片");
                return Task.FromResult(textElements);
            }

            var slideIdList = presentationPart.Presentation.SlideIdList;
            var slideIndex = 0;

            foreach (SlideId slideId in slideIdList)
            {
                slideIndex++;
                var slidePart = (SlidePart)presentationPart.GetPartById(slideId.RelationshipId!);
                var slide = slidePart.Slide;

                var shapeIndex = 0;
                foreach (var shape in slide.Descendants<Shape>())
                {
                    shapeIndex++;
                    var textBody = shape.TextBody;

                    if (textBody != null)
                    {
                        var textContent = ExtractTextFromTextBody(textBody);
                        if (!string.IsNullOrWhiteSpace(textContent))
                        {
                            var element = new PPTTextElement
                            {
                                SlideIndex = slideIndex,
                                ShapeIndex = shapeIndex,
                                Text = textContent,
                                Position = $"Slide {slideIndex}, Shape {shapeIndex}",
                                Format = GetTextFormat(textBody),
                                OriginalFontSize = GetFontSize(textBody),
                                FontName = GetFontName(textBody)
                            };

                            textElements.Add(element);
                        }
                    }
                }
            }

            _logger.LogInformation("从PPT中提取了 {Count} 个文本元素", textElements.Count);
            return Task.FromResult(textElements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取PPT文本元素失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<PPTTranslationResult> TranslatePPTAsync(
        string inputPath, 
        string outputPath, 
        string sourceLanguage, 
        string targetLanguage, 
        bool useTerminology = true,
        IProgress<TranslationProgress>? progressCallback = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PPTTranslationResult();

        try
        {
            // 1. 创建PPT副本
            await CreateCopyAsync(inputPath, outputPath);
            result.OutputPath = outputPath;

            // 2. 提取文本元素
            var textElements = await ExtractTextElementsAsync(outputPath);
            result.TotalSlides = textElements.GroupBy(e => e.SlideIndex).Count();
            result.ProcessedTextCount = textElements.Count;

            // 3. 翻译文本元素
            var totalElements = textElements.Count;
            var processedElements = 0;

            foreach (var element in textElements)
            {
                try
                {
                    // 报告进度
                    var progress = new TranslationProgress
                    {
                        CurrentSlide = element.SlideIndex,
                        TotalSlides = result.TotalSlides,
                        CurrentShape = element.ShapeIndex,
                        CurrentText = element.Text,
                        ProgressPercentage = (double)processedElements / totalElements * 100,
                        StatusMessage = $"正在翻译第 {element.SlideIndex} 页，第 {element.ShapeIndex} 个形状"
                    };
                    progressCallback?.Report(progress);

                    // 预处理文本
                    var preprocessResult = _textPreprocessor.PreprocessText(
                        element.Text, sourceLanguage, targetLanguage, useTerminology);

                    // 翻译文本
                    var translationRequest = new TranslationRequest
                    {
                        Text = preprocessResult.ProcessedText,
                        SourceLanguage = sourceLanguage,
                        TargetLanguage = targetLanguage,
                        UseTerminology = useTerminology,
                        Context = "PowerPoint presentation"
                    };

                    var translationResponse = await _translationService.TranslateAsync(translationRequest);

                    if (translationResponse.IsSuccess)
                    {
                        // 后处理文本
                        var finalText = _textPreprocessor.PostprocessText(
                            translationResponse.TranslatedText, preprocessResult);

                        element.TranslatedText = finalText;
                        element.IsTranslated = true;
                        element.TranslatedAt = DateTime.Now;

                        // 计算字体调整
                        element.AdjustedFontSize = CalculateAdjustedFontSize(
                            element.Text, finalText, element.OriginalFontSize);

                        // 更新统计信息
                        result.Statistics.TotalCharacters += element.Text.Length;
                        result.Statistics.TotalWords += element.Text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
                        if (preprocessResult.TerminologyResult?.Matches.Any() == true)
                        {
                            result.Statistics.TerminologyUsageCount += preprocessResult.TerminologyResult.Matches.Count;
                        }

                        // 报告翻译完成
                        progress.TranslatedText = finalText;
                        progressCallback?.Report(progress);
                    }
                    else
                    {
                        _logger.LogWarning("翻译失败: {Error}", translationResponse.ErrorMessage);
                        element.TranslatedText = element.Text; // 保持原文
                    }

                    processedElements++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "翻译文本元素失败: {Text}", element.Text);
                    element.TranslatedText = element.Text; // 保持原文
                    processedElements++;
                }
            }

            // 4. 替换PPT中的文本
            await ReplaceTextInPPTAsync(outputPath, textElements);

            // 5. 调整字体大小
            await AdjustFontSizesAsync(outputPath, textElements);

            result.TranslatedElements = textElements;
            result.IsSuccess = true;

            stopwatch.Stop();
            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;

            // 计算平均翻译时间
            if (result.Statistics.TotalCharacters > 0)
            {
                result.Statistics.AverageTranslationTimePerCharacter = 
                    (double)result.ElapsedMilliseconds / result.Statistics.TotalCharacters;
            }

            // 报告完成
            progressCallback?.Report(new TranslationProgress
            {
                ProgressPercentage = 100,
                StatusMessage = "翻译完成",
                IsCompleted = true
            });

            _logger.LogInformation("PPT翻译完成，耗时: {ElapsedMs}ms", result.ElapsedMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PPT翻译失败: {InputPath}", inputPath);
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;

            progressCallback?.Report(new TranslationProgress
            {
                HasError = true,
                ErrorMessage = ex.Message,
                StatusMessage = "翻译失败"
            });

            return result;
        }
    }

    public Task<bool> ReplaceTextInPPTAsync(string filePath, List<PPTTextElement> textElements)
    {
        try
        {
            using var presentationDocument = PresentationDocument.Open(filePath, true);
            var presentationPart = presentationDocument.PresentationPart;
            
            if (presentationPart?.Presentation?.SlideIdList == null)
            {
                return Task.FromResult(false);
            }

            var slideIdList = presentationPart.Presentation.SlideIdList;
            var slideIndex = 0;

            foreach (SlideId slideId in slideIdList)
            {
                slideIndex++;
                var slidePart = (SlidePart)presentationPart.GetPartById(slideId.RelationshipId!);
                var slide = slidePart.Slide;

                var shapeIndex = 0;
                foreach (var shape in slide.Descendants<Shape>())
                {
                    shapeIndex++;
                    var textBody = shape.TextBody;
                    
                    if (textBody != null)
                    {
                        var element = textElements.FirstOrDefault(e => 
                            e.SlideIndex == slideIndex && e.ShapeIndex == shapeIndex);

                        if (element != null && element.IsTranslated)
                        {
                            ReplaceTextInTextBody(textBody, element.TranslatedText);
                        }
                    }
                }
            }

            presentationDocument.Save();
            _logger.LogInformation("PPT文本替换完成");
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "替换PPT文本失败: {FilePath}", filePath);
            return Task.FromResult(false);
        }
    }

    public Task<bool> AdjustFontSizesAsync(string filePath, List<PPTTextElement> textElements)
    {
        try
        {
            using var presentationDocument = PresentationDocument.Open(filePath, true);
            var presentationPart = presentationDocument.PresentationPart;
            
            if (presentationPart?.Presentation?.SlideIdList == null)
            {
                return Task.FromResult(false);
            }

            var slideIdList = presentationPart.Presentation.SlideIdList;
            var slideIndex = 0;
            var adjustmentCount = 0;

            foreach (SlideId slideId in slideIdList)
            {
                slideIndex++;
                var slidePart = (SlidePart)presentationPart.GetPartById(slideId.RelationshipId!);
                var slide = slidePart.Slide;

                var shapeIndex = 0;
                foreach (var shape in slide.Descendants<Shape>())
                {
                    shapeIndex++;
                    var textBody = shape.TextBody;
                    
                    if (textBody != null)
                    {
                        var element = textElements.FirstOrDefault(e => 
                            e.SlideIndex == slideIndex && e.ShapeIndex == shapeIndex);

                        if (element != null && element.IsTranslated && 
                            Math.Abs(element.AdjustedFontSize - element.OriginalFontSize) > 0.1)
                        {
                            AdjustFontSizeInTextBody(textBody, element.AdjustedFontSize);
                            adjustmentCount++;
                        }
                    }
                }
            }

            presentationDocument.Save();
            _logger.LogInformation("字体大小调整完成，调整了 {Count} 个文本元素", adjustmentCount);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调整字体大小失败: {FilePath}", filePath);
            return Task.FromResult(false);
        }
    }

    public Task<bool> CreateCopyAsync(string sourcePath, string destinationPath)
    {
        try
        {
            var destinationDir = Path.GetDirectoryName(destinationPath);
            if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
            {
                Directory.CreateDirectory(destinationDir);
            }

            File.Copy(sourcePath, destinationPath, true);
            _logger.LogInformation("PPT副本创建成功: {DestinationPath}", destinationPath);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建PPT副本失败: {SourcePath} -> {DestinationPath}", sourcePath, destinationPath);
            return Task.FromResult(false);
        }
    }

    public Task<PPTValidationResult> ValidatePPTAsync(string filePath)
    {
        var result = new PPTValidationResult();

        try
        {
            if (!File.Exists(filePath))
            {
                result.Errors.Add("文件不存在");
                return Task.FromResult(result);
            }

            var fileInfo = new FileInfo(filePath);
            result.FileSize = fileInfo.Length;
            result.FileFormat = Path.GetExtension(filePath).ToLowerInvariant();

            // 验证文件格式
            if (result.FileFormat != ".pptx" && result.FileFormat != ".ppt")
            {
                result.Errors.Add("不支持的文件格式，仅支持 .pptx 和 .ppt 文件");
                return Task.FromResult(result);
            }

            // 尝试打开文件
            using var presentationDocument = PresentationDocument.Open(filePath, false);
            var presentationPart = presentationDocument.PresentationPart;
            
            if (presentationPart?.Presentation == null)
            {
                result.Errors.Add("无法读取PPT内容");
                return Task.FromResult(result);
            }

            result.IsValid = true;
            _logger.LogInformation("PPT文件验证通过: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"文件验证失败: {ex.Message}");
            _logger.LogError(ex, "PPT文件验证失败: {FilePath}", filePath);
        }

        return Task.FromResult(result);
    }

    public async Task<PPTInfo> GetPPTInfoAsync(string filePath)
    {
        var info = new PPTInfo
        {
            FileName = Path.GetFileName(filePath)
        };

        try
        {
            var fileInfo = new FileInfo(filePath);
            info.FileSize = fileInfo.Length;
            info.CreatedAt = fileInfo.CreationTime;
            info.ModifiedAt = fileInfo.LastWriteTime;

            using var presentationDocument = PresentationDocument.Open(filePath, false);
            var presentationPart = presentationDocument.PresentationPart;
            
            if (presentationPart?.Presentation?.SlideIdList != null)
            {
                info.SlideCount = presentationPart.Presentation.SlideIdList.Count();
                
                // 统计文本元素数量
                var textElements = await ExtractTextElementsAsync(filePath);
                info.TextElementCount = textElements.Count;
            }

            // 获取文档属性
            var coreProperties = presentationDocument.PackageProperties;
            info.Author = coreProperties.Creator ?? "";
            info.Title = coreProperties.Title ?? "";
            info.Subject = coreProperties.Subject ?? "";

            _logger.LogInformation("获取PPT信息成功: {FileName}", info.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取PPT信息失败: {FilePath}", filePath);
        }

        return info;
    }

    #region 私有辅助方法

    private string ExtractTextFromTextBody(P.TextBody textBody)
    {
        var textContent = string.Empty;
        
        foreach (var paragraph in textBody.Descendants<A.Paragraph>())
        {
            foreach (var run in paragraph.Descendants<A.Run>())
            {
                var text = run.Descendants<A.Text>().FirstOrDefault();
                if (text != null)
                {
                    textContent += text.Text;
                }
            }
            textContent += Environment.NewLine;
        }

        return textContent.Trim();
    }

    private void ReplaceTextInTextBody(P.TextBody textBody, string newText)
    {
        // 清除现有文本
        textBody.RemoveAllChildren<A.Paragraph>();

        // 创建新段落
        var paragraph = new A.Paragraph();
        var run = new A.Run();
        var text = new A.Text { Text = newText };

        run.Append(text);
        paragraph.Append(run);
        textBody.Append(paragraph);
    }

    private string GetTextFormat(P.TextBody textBody)
    {
        // 获取文本格式信息（字体、大小、颜色等）
        var format = "Normal";
        
        var firstRun = textBody.Descendants<A.Run>().FirstOrDefault();
        if (firstRun?.RunProperties != null)
        {
            var runProps = firstRun.RunProperties;
            if (runProps.Bold?.Value == true) format += ", Bold";
            if (runProps.Italic?.Value == true) format += ", Italic";
            if (runProps.Underline != null) format += ", Underline";
        }

        return format;
    }

    private double GetFontSize(P.TextBody textBody)
    {
        var firstRun = textBody.Descendants<A.Run>().FirstOrDefault();
        if (firstRun?.RunProperties?.FontSize?.Value != null)
        {
            return firstRun.RunProperties.FontSize.Value / 100.0; // OpenXML中字体大小以百分点为单位
        }
        return 12.0; // 默认字体大小
    }

    private string GetFontName(P.TextBody textBody)
    {
        try
        {
            var firstRun = textBody.Descendants<A.Run>().FirstOrDefault();
            var latinFont = firstRun?.RunProperties?.Descendants<A.LatinFont>().FirstOrDefault();
            var typeface = latinFont?.Typeface;
            return string.IsNullOrEmpty(typeface) ? "Arial" : typeface;
        }
        catch
        {
            return "Arial"; // 默认字体
        }
    }

    private void AdjustFontSizeInTextBody(P.TextBody textBody, double newFontSize)
    {
        var fontSizeValue = (int)(newFontSize * 100); // 转换为百分点

        foreach (var run in textBody.Descendants<A.Run>())
        {
            if (run.RunProperties == null)
            {
                run.RunProperties = new A.RunProperties();
            }

            run.RunProperties.FontSize = fontSizeValue;
        }
    }

    private double CalculateAdjustedFontSize(string originalText, string translatedText, double originalFontSize)
    {
        if (string.IsNullOrEmpty(originalText) || string.IsNullOrEmpty(translatedText))
        {
            return originalFontSize;
        }

        // 简单的字体大小调整算法：基于文本长度比例
        var lengthRatio = (double)originalText.Length / translatedText.Length;
        
        // 如果翻译后文本更长，适当减小字体
        if (lengthRatio < 0.8)
        {
            return originalFontSize * Math.Max(0.7, lengthRatio);
        }
        // 如果翻译后文本更短，可以适当增大字体（但不超过原始大小的1.2倍）
        else if (lengthRatio > 1.2)
        {
            return originalFontSize * Math.Min(1.2, lengthRatio * 0.8);
        }

        return originalFontSize;
    }

    #endregion
}
