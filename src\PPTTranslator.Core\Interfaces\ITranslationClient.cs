namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// 翻译客户端接口
/// </summary>
public interface ITranslationClient
{
    /// <summary>
    /// 翻译文本
    /// </summary>
    /// <param name="text">待翻译文本</param>
    /// <param name="sourceLanguage">源语言</param>
    /// <param name="targetLanguage">目标语言</param>
    /// <param name="model">使用的模型</param>
    /// <returns>翻译结果</returns>
    Task<string> TranslateAsync(string text, string sourceLanguage, string targetLanguage, string model);

    /// <summary>
    /// 检测文本语言
    /// </summary>
    /// <param name="text">文本</param>
    /// <returns>语言代码</returns>
    Task<string> DetectLanguageAsync(string text);

    /// <summary>
    /// 验证连接
    /// </summary>
    /// <returns>是否连接成功</returns>
    Task<bool> ValidateConnectionAsync();

    /// <summary>
    /// 获取可用模型列表
    /// </summary>
    /// <returns>模型列表</returns>
    Task<List<string>> GetAvailableModelsAsync();
}

/// <summary>
/// 翻译客户端配置
/// </summary>
public class TranslationClientConfig
{
    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string ServerUrl { get; set; } = string.Empty;

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// 默认模型
    /// </summary>
    public string DefaultModel { get; set; } = string.Empty;
}
