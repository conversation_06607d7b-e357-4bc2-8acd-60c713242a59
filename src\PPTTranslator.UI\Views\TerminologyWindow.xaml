<Window x:Class="PPTTranslator.UI.Views.TerminologyWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:PPTTranslator.UI.ViewModels"
        xmlns:converters="clr-namespace:PPTTranslator.UI.Converters"
        mc:Ignorable="d"
        Title="术语库管理" 
        Height="700" 
        Width="1000"
        MinHeight="500"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="TerminologyDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="术语库管理" 
                               Style="{StaticResource HeaderTextStyle}"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="导入" 
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding ImportCommand}"
                                Margin="4,0"/>
                        <Button Content="导出" 
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding ExportCommand}"
                                Margin="4,0"/>
                        <Button Content="刷新" 
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding RefreshCommand}"
                                Margin="4,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区域 -->
            <Grid Grid.Row="1" Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧术语列表 -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 标题和搜索 -->
                        <TextBlock Grid.Row="0" 
                                   Text="术语列表" 
                                   Style="{StaticResource SubHeaderTextStyle}"
                                   Margin="0,0,0,8"/>

                        <TextBox Grid.Row="1"
                                 Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="搜索术语..."
                                 Margin="0,0,0,8"/>

                        <!-- 术语列表 -->
                        <DataGrid Grid.Row="2"
                                  ItemsSource="{Binding FilteredEntries}"
                                  SelectedItem="{Binding SelectedEntry}"
                                  Style="{StaticResource DataGridStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="源术语" Binding="{Binding SourceTerm}" Width="*"/>
                                <DataGridTextColumn Header="目标术语" Binding="{Binding TargetTerm}" Width="*"/>
                                <DataGridTextColumn Header="类别" Binding="{Binding Category}" Width="80"/>
                                <DataGridTextColumn Header="优先级" Binding="{Binding Priority}" Width="60"/>
                                <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="50"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- 统计信息 -->
                        <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,8,0,0">
                            <TextBlock Text="总计: " Style="{StaticResource BodyTextStyle}"/>
                            <TextBlock Text="{Binding TerminologyEntries.Count}" Style="{StaticResource BodyTextStyle}" FontWeight="Bold"/>
                            <TextBlock Text=" 条术语" Style="{StaticResource BodyTextStyle}" Margin="0,0,16,0"/>
                            
                            <TextBlock Text="显示: " Style="{StaticResource BodyTextStyle}"/>
                            <TextBlock Text="{Binding FilteredEntries.Count}" Style="{StaticResource BodyTextStyle}" FontWeight="Bold"/>
                            <TextBlock Text=" 条" Style="{StaticResource BodyTextStyle}"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 右侧编辑面板 -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Style="{StaticResource SubHeaderTextStyle}" Margin="0,0,0,16">
                            <TextBlock.Text>
                                <Binding Path="IsEditMode" Converter="{StaticResource BooleanToStringConverter}" ConverterParameter="编辑术语|添加术语"/>
                            </TextBlock.Text>
                        </TextBlock>

                        <!-- 源术语 -->
                        <TextBox Text="{Binding NewSourceTerm, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="源术语"
                                 Margin="0,0,0,8"/>

                        <!-- 目标术语 -->
                        <TextBox Text="{Binding NewTargetTerm, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="目标术语"
                                 Margin="0,0,0,8"/>

                        <!-- 类别 -->
                        <ComboBox ItemsSource="{Binding Categories}"
                                  Text="{Binding NewCategory}"
                                  IsEditable="True"
                                  Style="{StaticResource InputComboBoxStyle}"
                                  materialDesign:HintAssist.Hint="类别"
                                  Margin="0,0,0,8"/>

                        <!-- 优先级 -->
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="优先级:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <Slider Grid.Column="1" 
                                    Value="{Binding NewPriority}"
                                    Minimum="1" Maximum="10"
                                    TickFrequency="1"
                                    IsSnapToTickEnabled="True"
                                    TickPlacement="BottomRight"/>
                        </Grid>

                        <!-- 描述 -->
                        <TextBox Text="{Binding NewDescription}"
                                 Style="{StaticResource InputTextBoxStyle}"
                                 materialDesign:HintAssist.Hint="描述（可选）"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 MinLines="2"
                                 MaxLines="4"
                                 Margin="0,0,0,16"/>

                        <!-- 操作按钮 -->
                        <StackPanel>
                            <Button Content="添加术语"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Command="{Binding AddEntryCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,0,0,8"
                                    Visibility="{Binding IsEditMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                            <Button Content="更新术语"
                                    Style="{StaticResource PrimaryButtonStyle}"
                                    Command="{Binding UpdateEntryCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,0,0,8"
                                    Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <Button Content="清空表单"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Command="{Binding ClearFormCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,0,0,8"/>

                            <Button Content="删除选中"
                                    Style="{StaticResource DangerButtonStyle}"
                                    Command="{Binding DeleteEntryCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,0,0,8"/>

                            <Button Content="清空所有"
                                    Style="{StaticResource DangerButtonStyle}"
                                    Command="{Binding ClearAllCommand}"
                                    HorizontalAlignment="Stretch"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 状态栏 -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="{Binding StatusMessage}" 
                               Style="{StaticResource BodyTextStyle}"
                               VerticalAlignment="Center"/>
                    
                    <TextBlock Grid.Column="1" 
                               Text="{Binding ErrorMessage}" 
                               Style="{StaticResource BodyTextStyle}"
                               Foreground="{StaticResource ErrorBrush}"
                               VerticalAlignment="Center"
                               Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window>
