using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Win32;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Services;

namespace PPTTranslator.SimpleUI;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    private readonly ITerminologyManager _terminologyManager;
    private readonly ITextPreprocessor _textPreprocessor;
    private readonly IFontAdjustmentService _fontAdjustmentService;
    private readonly ILogger<MainWindow> _logger;

    public MainWindow()
    {
        InitializeComponent();

        // 简单的服务初始化（不使用依赖注入）
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<MainWindow>();
        
        var terminologyLogger = loggerFactory.CreateLogger<TerminologyManager>();
        _terminologyManager = new TerminologyManager(terminologyLogger);

        var preprocessorLogger = loggerFactory.CreateLogger<TextPreprocessor>();
        _textPreprocessor = new TextPreprocessor(_terminologyManager, preprocessorLogger);

        var fontLogger = loggerFactory.CreateLogger<FontAdjustmentService>();
        _fontAdjustmentService = new FontAdjustmentService(fontLogger);

        // 加载默认术语库
        LoadDefaultTerminology();

        StatusTextBlock.Text = "应用初始化完成";
    }

    private async void LoadDefaultTerminology()
    {
        try
        {
            var terminologyPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "terminology.json");
            if (File.Exists(terminologyPath))
            {
                await _terminologyManager.LoadTerminologyAsync(terminologyPath);
                StatusTextBlock.Text = $"已加载术语库，共 {_terminologyManager.GetAllEntries().Count()} 条术语";
            }
            else
            {
                // 添加一些示例术语
                _terminologyManager.AddTerminologyEntry(new PPTTranslator.Core.Models.TerminologyEntry
                {
                    SourceTerm = "人工智能",
                    TargetTerm = "Artificial Intelligence",
                    Category = "技术"
                });
                _terminologyManager.AddTerminologyEntry(new PPTTranslator.Core.Models.TerminologyEntry
                {
                    SourceTerm = "机器学习",
                    TargetTerm = "Machine Learning",
                    Category = "技术"
                });
                StatusTextBlock.Text = "已加载默认术语库";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载术语库失败");
            StatusTextBlock.Text = "术语库加载失败";
        }
    }

    private void SelectFileButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "选择PPT文件",
            Filter = "PowerPoint文件|*.pptx;*.ppt|所有文件|*.*",
            CheckFileExists = true
        };

        if (openFileDialog.ShowDialog() == true)
        {
            FilePathTextBox.Text = openFileDialog.FileName;
            TranslateButton.IsEnabled = true;
            StatusTextBlock.Text = "已选择文件: " + Path.GetFileName(openFileDialog.FileName);
        }
    }

    private async void TranslateButton_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(FilePathTextBox.Text))
        {
            MessageBox.Show("请先选择PPT文件", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            TranslateButton.IsEnabled = false;
            StatusTextBlock.Text = "正在处理...";
            ResultTextBox.Clear();

            // 模拟PPT处理过程
            await SimulatePPTProcessing();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "翻译过程出错");
            MessageBox.Show($"翻译失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            TranslateButton.IsEnabled = true;
            StatusTextBlock.Text = "就绪";
        }
    }

    private async Task SimulatePPTProcessing()
    {
        var results = new List<string>();
        
        // 模拟文本提取
        StatusTextBlock.Text = "正在提取文本...";
        await Task.Delay(500);
        
        var sampleTexts = new[]
        {
            "人工智能技术发展",
            "机器学习算法优化",
            "深度学习模型训练",
            "数据科学应用实践"
        };

        results.Add("=== PPT文本处理演示 ===\n");

        foreach (var text in sampleTexts)
        {
            // 语言检测
            var detectedLang = _textPreprocessor.DetectLanguage(text);
            results.Add($"原文: {text}");
            results.Add($"检测语言: {detectedLang}");

            // 术语预处理
            var sourceLanguage = LanguageComboBox.SelectedIndex == 0 ? "zh-CN" : "en";
            var targetLanguage = LanguageComboBox.SelectedIndex == 0 ? "en" : "zh-CN";

            if (UseTerminologyCheckBox.IsChecked == true)
            {
                var preprocessResult = _terminologyManager.PreprocessText(text, sourceLanguage, targetLanguage);
                if (preprocessResult.Matches.Any())
                {
                    results.Add($"术语匹配: {preprocessResult.Matches.Count} 个");
                    foreach (var match in preprocessResult.Matches)
                    {
                        results.Add($"  - {match.MatchedText} → {match.ReplacementText}");
                    }
                }
            }

            // 模拟翻译
            var translatedText = SimulateTranslation(text, LanguageComboBox.SelectedIndex == 0);
            results.Add($"翻译结果: {translatedText}");

            // 字体调整
            var adjustedFontSize = _fontAdjustmentService.CalculateAdjustedFontSize(text, translatedText, 12.0);
            results.Add($"字体调整: 12.0 → {adjustedFontSize:F1}");

            results.Add(""); // 空行分隔

            // 更新UI
            ResultTextBox.Text = string.Join("\n", results);
            StatusTextBlock.Text = $"正在处理文本 ({Array.IndexOf(sampleTexts, text) + 1}/{sampleTexts.Length})...";
            
            await Task.Delay(800); // 模拟处理时间
        }

        StatusTextBlock.Text = "处理完成";
        results.Add("=== 处理完成 ===");
        results.Add($"共处理 {sampleTexts.Length} 个文本元素");
        results.Add($"使用术语库: {(UseTerminologyCheckBox.IsChecked == true ? "是" : "否")}");
        results.Add($"翻译方向: {((ComboBoxItem)LanguageComboBox.SelectedItem).Content}");

        ResultTextBox.Text = string.Join("\n", results);
    }

    private string SimulateTranslation(string text, bool chineseToEnglish)
    {
        // 简单的模拟翻译
        if (chineseToEnglish)
        {
            return text switch
            {
                "人工智能技术发展" => "Artificial Intelligence Technology Development",
                "机器学习算法优化" => "Machine Learning Algorithm Optimization",
                "深度学习模型训练" => "Deep Learning Model Training",
                "数据科学应用实践" => "Data Science Application Practice",
                _ => $"[Translated] {text}"
            };
        }
        else
        {
            return $"[翻译] {text}";
        }
    }
}
