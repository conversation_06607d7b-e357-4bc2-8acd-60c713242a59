using System.Diagnostics;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 翻译服务实现
/// </summary>
public class TranslationService : ITranslationService
{
    private readonly ITranslationClient _translationClient;
    private readonly ILogger<TranslationService> _logger;
    private readonly Dictionary<string, string> _translationCache;

    public TranslationService(ITranslationClient translationClient, ILogger<TranslationService> logger)
    {
        _translationClient = translationClient;
        _logger = logger;
        _translationCache = new Dictionary<string, string>();
    }

    public async Task<TranslationResponse> TranslateAsync(TranslationRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new TranslationResponse
        {
            OriginalText = request.Text
        };

        try
        {
            if (string.IsNullOrWhiteSpace(request.Text))
            {
                response.TranslatedText = request.Text;
                response.IsSuccess = true;
                return response;
            }

            // 检查缓存
            var cacheKey = GenerateCacheKey(request);
            if (_translationCache.TryGetValue(cacheKey, out var cachedTranslation))
            {
                response.TranslatedText = cachedTranslation;
                response.IsSuccess = true;
                response.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                _logger.LogDebug("使用缓存翻译结果: {Text}", request.Text.Substring(0, Math.Min(50, request.Text.Length)));
                return response;
            }

            // 执行翻译
            var translatedText = await _translationClient.TranslateAsync(
                request.Text, 
                request.SourceLanguage, 
                request.TargetLanguage, 
                request.ModelType);

            if (!string.IsNullOrEmpty(translatedText))
            {
                response.TranslatedText = translatedText;
                response.IsSuccess = true;
                response.Model = request.ModelType;

                // 缓存翻译结果
                _translationCache[cacheKey] = translatedText;

                _logger.LogDebug("翻译成功: {OriginalLength} -> {TranslatedLength} 字符", 
                    request.Text.Length, translatedText.Length);
            }
            else
            {
                response.IsSuccess = false;
                response.ErrorMessage = "翻译结果为空";
                _logger.LogWarning("翻译结果为空: {Text}", request.Text);
            }
        }
        catch (Exception ex)
        {
            response.IsSuccess = false;
            response.ErrorMessage = ex.Message;
            _logger.LogError(ex, "翻译失败: {Text}", request.Text);
        }

        stopwatch.Stop();
        response.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
        return response;
    }

    public async Task<List<TranslationResponse>> TranslateBatchAsync(List<TranslationRequest> requests)
    {
        var responses = new List<TranslationResponse>();
        var tasks = new List<Task<TranslationResponse>>();

        // 并发翻译（限制并发数量以避免API限制）
        var semaphore = new SemaphoreSlim(5, 5); // 最多5个并发请求

        foreach (var request in requests)
        {
            tasks.Add(TranslateWithSemaphoreAsync(request, semaphore));
        }

        var results = await Task.WhenAll(tasks);
        responses.AddRange(results);

        _logger.LogInformation("批量翻译完成: {RequestCount} 个请求", requests.Count);
        return responses;
    }

    public async Task<string> DetectLanguageAsync(string text)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return "unknown";
            }

            var detectedLanguage = await _translationClient.DetectLanguageAsync(text);
            _logger.LogDebug("语言检测结果: {Language} for text: {Text}", 
                detectedLanguage, text.Substring(0, Math.Min(50, text.Length)));
            
            return detectedLanguage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "语言检测失败: {Text}", text);
            return "unknown";
        }
    }

    public Task<List<SupportedLanguage>> GetSupportedLanguagesAsync()
    {
        // 返回常用的支持语言列表
        return Task.FromResult(new List<SupportedLanguage>
        {
            new() { Code = "zh-CN", Name = "Chinese (Simplified)", NativeName = "中文（简体）" },
            new() { Code = "zh-TW", Name = "Chinese (Traditional)", NativeName = "中文（繁體）" },
            new() { Code = "en", Name = "English", NativeName = "English" },
            new() { Code = "ja", Name = "Japanese", NativeName = "日本語" },
            new() { Code = "ko", Name = "Korean", NativeName = "한국어" },
            new() { Code = "fr", Name = "French", NativeName = "Français" },
            new() { Code = "de", Name = "German", NativeName = "Deutsch" },
            new() { Code = "es", Name = "Spanish", NativeName = "Español" },
            new() { Code = "ru", Name = "Russian", NativeName = "Русский" },
            new() { Code = "pt", Name = "Portuguese", NativeName = "Português" },
            new() { Code = "it", Name = "Italian", NativeName = "Italiano" },
            new() { Code = "ar", Name = "Arabic", NativeName = "العربية" }
        });
    }

    public async Task<bool> ValidateConnectionAsync()
    {
        try
        {
            var isValid = await _translationClient.ValidateConnectionAsync();
            _logger.LogInformation("翻译服务连接验证结果: {IsValid}", isValid);
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "翻译服务连接验证失败");
            return false;
        }
    }

    public async Task<TranslationServiceInfo> GetServiceInfoAsync()
    {
        try
        {
            var availableModels = await _translationClient.GetAvailableModelsAsync();
            var isConnected = await _translationClient.ValidateConnectionAsync();

            return new TranslationServiceInfo
            {
                ServiceName = "PPT Translator",
                Version = "1.0.0",
                Provider = _translationClient.GetType().Name,
                Status = isConnected ? "Connected" : "Disconnected",
                MaxTextLength = 5000,
                RequestsPerMinute = 60,
                SupportedModels = availableModels
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取翻译服务信息失败");
            return new TranslationServiceInfo
            {
                ServiceName = "PPT Translator",
                Version = "1.0.0",
                Provider = "Unknown",
                Status = "Error"
            };
        }
    }

    private async Task<TranslationResponse> TranslateWithSemaphoreAsync(TranslationRequest request, SemaphoreSlim semaphore)
    {
        await semaphore.WaitAsync();
        try
        {
            return await TranslateAsync(request);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private string GenerateCacheKey(TranslationRequest request)
    {
        var keyComponents = new[]
        {
            request.Text,
            request.SourceLanguage,
            request.TargetLanguage,
            request.ModelType,
            request.UseTerminology.ToString()
        };

        var combinedKey = string.Join("|", keyComponents);
        return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(combinedKey));
    }

    /// <summary>
    /// 清理翻译缓存
    /// </summary>
    public void ClearCache()
    {
        _translationCache.Clear();
        _logger.LogInformation("翻译缓存已清理");
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public (int Count, long MemoryUsage) GetCacheStats()
    {
        var count = _translationCache.Count;
        var memoryUsage = _translationCache.Sum(kvp => 
            System.Text.Encoding.UTF8.GetByteCount(kvp.Key) + 
            System.Text.Encoding.UTF8.GetByteCount(kvp.Value));

        return (count, memoryUsage);
    }
}
