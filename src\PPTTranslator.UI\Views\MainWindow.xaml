<Window x:Class="PPTTranslator.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:PPTTranslator.UI.ViewModels"
        xmlns:converters="clr-namespace:PPTTranslator.UI.Converters"
        mc:Ignorable="d"
        Title="PPT翻译工具" 
        Height="800" 
        Width="1200"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <converters:FileSizeConverter x:Key="FileSizeConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="PPT翻译工具" 
                               Style="{StaticResource HeaderTextStyle}"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="术语库管理"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding ManageTerminologyCommand}"
                                Margin="4,0"/>
                        <Button Content="验证连接"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Command="{Binding ValidateConnectionCommand}"
                                Margin="4,0"/>
                        <Button Content="设置"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="4,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="16"/>
                        <ColumnDefinition Width="3*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧配置面板 -->
                    <StackPanel Grid.Column="0">
                        
                        <!-- 文件选择 -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="文件选择" Style="{StaticResource SubHeaderTextStyle}"/>
                                
                                <Button Content="选择PPT文件" 
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Command="{Binding SelectFileCommand}"
                                        HorizontalAlignment="Stretch"
                                        Margin="0,8"/>
                                
                                <TextBox Text="{Binding SelectedFilePath, Mode=OneWay}"
                                         Style="{StaticResource InputTextBoxStyle}"
                                         IsReadOnly="True"
                                         materialDesign:HintAssist.Hint="选中的文件路径"
                                         Margin="0,4"/>
                                
                                <TextBox Text="{Binding OutputFilePath}"
                                         Style="{StaticResource InputTextBoxStyle}"
                                         materialDesign:HintAssist.Hint="输出文件路径"
                                         Margin="0,4"/>
                            </StackPanel>
                        </Border>

                        <!-- 翻译配置 -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="翻译配置" Style="{StaticResource SubHeaderTextStyle}"/>
                                
                                <ComboBox ItemsSource="{Binding SupportedLanguages}"
                                          SelectedValue="{Binding SourceLanguage}"
                                          SelectedValuePath="Code"
                                          DisplayMemberPath="Name"
                                          Style="{StaticResource InputComboBoxStyle}"
                                          materialDesign:HintAssist.Hint="源语言"
                                          Margin="0,4"/>
                                
                                <ComboBox ItemsSource="{Binding SupportedLanguages}"
                                          SelectedValue="{Binding TargetLanguage}"
                                          SelectedValuePath="Code"
                                          DisplayMemberPath="Name"
                                          Style="{StaticResource InputComboBoxStyle}"
                                          materialDesign:HintAssist.Hint="目标语言"
                                          Margin="0,4"/>
                                
                                <ComboBox ItemsSource="{Binding AvailableModels}"
                                          SelectedItem="{Binding SelectedModel}"
                                          Style="{StaticResource InputComboBoxStyle}"
                                          materialDesign:HintAssist.Hint="翻译模型"
                                          Margin="0,4"/>
                                
                                <CheckBox Content="使用术语库"
                                          IsChecked="{Binding UseTerminology}"
                                          Margin="0,8"/>
                            </StackPanel>
                        </Border>

                        <!-- PPT信息 -->
                        <Border Style="{StaticResource CardStyle}" 
                                Visibility="{Binding HasSelectedFile, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="文件信息" Style="{StaticResource SubHeaderTextStyle}"/>
                                
                                <Grid Margin="0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="文件名：" Style="{StaticResource BodyTextStyle}"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding PPTInfo.FileName}" Style="{StaticResource BodyTextStyle}"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="页数：" Style="{StaticResource BodyTextStyle}"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding PPTInfo.SlideCount}" Style="{StaticResource BodyTextStyle}"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="文本元素：" Style="{StaticResource BodyTextStyle}"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PPTInfo.TextElementCount}" Style="{StaticResource BodyTextStyle}"/>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="文件大小：" Style="{StaticResource BodyTextStyle}"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Style="{StaticResource BodyTextStyle}">
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="{}{0:F1} KB">
                                                <Binding Path="PPTInfo.FileSize" Converter="{StaticResource FileSizeConverter}"/>
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 操作按钮 -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <Button Content="开始翻译" 
                                        Style="{StaticResource SuccessButtonStyle}"
                                        Command="{Binding TranslateCommand}"
                                        HorizontalAlignment="Stretch"
                                        Margin="0,4"/>
                                
                                <Button Content="打开输出文件夹" 
                                        Style="{StaticResource SecondaryButtonStyle}"
                                        Command="{Binding OpenOutputFolderCommand}"
                                        HorizontalAlignment="Stretch"
                                        Margin="0,4"/>
                            </StackPanel>
                        </Border>

                    </StackPanel>

                    <!-- 右侧结果面板 -->
                    <StackPanel Grid.Column="2">
                        
                        <!-- 进度显示 -->
                        <Border Style="{StaticResource CardStyle}"
                                Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="翻译进度" Style="{StaticResource SubHeaderTextStyle}"/>
                                
                                <ProgressBar Value="{Binding ProgressValue}"
                                             Style="{StaticResource CustomProgressBarStyle}"
                                             Height="8"
                                             Margin="0,8"/>
                                
                                <TextBlock Text="{Binding ProgressText}"
                                           Style="{StaticResource BodyTextStyle}"
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- 翻译结果 -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="翻译结果" Style="{StaticResource SubHeaderTextStyle}"/>
                                
                                <DataGrid ItemsSource="{Binding TranslationResults}"
                                          Style="{StaticResource CustomDataGridStyle}"
                                          MaxHeight="400">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="页面" Binding="{Binding SlideIndex}" Width="60"/>
                                        <DataGridTextColumn Header="位置" Binding="{Binding Position}" Width="100"/>
                                        <DataGridTextColumn Header="原文" Binding="{Binding Text}" Width="200">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                                    <Setter Property="MaxWidth" Value="200"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>
                                        <DataGridTextColumn Header="译文" Binding="{Binding TranslatedText}" Width="200">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                                    <Setter Property="MaxWidth" Value="200"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>
                                        <DataGridTemplateColumn Header="状态" Width="60">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <Ellipse Style="{StaticResource SuccessStatusStyle}"
                                                             Visibility="{Binding IsTranslated, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>

                    </StackPanel>

                </Grid>
            </ScrollViewer>

            <!-- 状态栏 -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}" Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="{Binding StatusMessage}" 
                               Style="{StaticResource BodyTextStyle}"
                               VerticalAlignment="Center"/>
                    
                    <TextBlock Grid.Column="1" 
                               Text="{Binding ErrorMessage}" 
                               Style="{StaticResource BodyTextStyle}"
                               Foreground="{StaticResource ErrorBrush}"
                               VerticalAlignment="Center"
                               Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>
            </Border>

        </Grid>
    </materialDesign:DialogHost>
</Window>
