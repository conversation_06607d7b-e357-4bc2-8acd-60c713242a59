using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 字体调整服务实现
/// </summary>
public class FontAdjustmentService : IFontAdjustmentService
{
    private readonly ILogger<FontAdjustmentService> _logger;
    
    // 不同语言的平均字符宽度比例（相对于英文）
    private readonly Dictionary<string, double> _languageCharacterWidthRatios = new()
    {
        { "zh-CN", 1.8 },  // 中文字符通常比英文宽
        { "zh-TW", 1.8 },
        { "ja", 1.6 },     // 日文
        { "ko", 1.5 },     // 韩文
        { "en", 1.0 },     // 英文基准
        { "fr", 1.1 },     // 法文
        { "de", 1.2 },     // 德文
        { "es", 1.1 },     // 西班牙文
        { "ru", 1.3 },     // 俄文
        { "ar", 1.4 }      // 阿拉伯文
    };

    public FontAdjustmentService(ILogger<FontAdjustmentService> logger)
    {
        _logger = logger;
    }

    public double CalculateAdjustedFontSize(
        string originalText, 
        string translatedText, 
        double originalFontSize, 
        double? maxWidth = null, 
        double? maxHeight = null)
    {
        try
        {
            if (string.IsNullOrEmpty(originalText) || string.IsNullOrEmpty(translatedText))
            {
                return originalFontSize;
            }

            // 计算文本长度比例
            var lengthRatio = (double)originalText.Length / translatedText.Length;
            
            // 估算字符宽度差异
            var originalLanguage = DetectLanguage(originalText);
            var translatedLanguage = DetectLanguage(translatedText);
            
            var originalCharWidth = _languageCharacterWidthRatios.GetValueOrDefault(originalLanguage, 1.0);
            var translatedCharWidth = _languageCharacterWidthRatios.GetValueOrDefault(translatedLanguage, 1.0);
            
            var charWidthRatio = originalCharWidth / translatedCharWidth;
            
            // 综合考虑长度和字符宽度
            var effectiveRatio = lengthRatio * charWidthRatio;
            
            // 计算调整后的字体大小
            var adjustedFontSize = originalFontSize * Math.Sqrt(effectiveRatio);
            
            // 应用约束
            var strategy = GetAdjustmentStrategy(originalLanguage, translatedLanguage);
            adjustedFontSize = Math.Max(strategy.MinFontSize, 
                Math.Min(strategy.MaxFontSize, adjustedFontSize));
            
            // 限制调整范围
            var maxScaleDown = originalFontSize * strategy.MaxScaleDown;
            var maxScaleUp = originalFontSize * strategy.MaxScaleUp;
            adjustedFontSize = Math.Max(maxScaleDown, Math.Min(maxScaleUp, adjustedFontSize));
            
            _logger.LogDebug("字体大小调整: {Original} -> {Adjusted} (比例: {Ratio:F2})", 
                originalFontSize, adjustedFontSize, adjustedFontSize / originalFontSize);
            
            return adjustedFontSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算字体调整失败");
            return originalFontSize;
        }
    }

    public List<FontAdjustmentResult> CalculateBatchAdjustments(List<PPTTextElement> textElements)
    {
        var results = new List<FontAdjustmentResult>();

        foreach (var element in textElements)
        {
            try
            {
                var originalSize = EstimateTextSize(element.Text, element.OriginalFontSize, element.FontName);
                var adjustedFontSize = CalculateAdjustedFontSize(
                    element.Text, element.TranslatedText, element.OriginalFontSize);
                var adjustedSize = EstimateTextSize(element.TranslatedText, adjustedFontSize, element.FontName);

                var result = new FontAdjustmentResult
                {
                    ElementId = element.Id,
                    OriginalFontSize = element.OriginalFontSize,
                    AdjustedFontSize = adjustedFontSize,
                    AdjustmentRatio = adjustedFontSize / element.OriginalFontSize,
                    RequiresAdjustment = Math.Abs(adjustedFontSize - element.OriginalFontSize) > 0.5,
                    OriginalTextSize = originalSize,
                    AdjustedTextSize = adjustedSize,
                    Confidence = CalculateConfidence(element.Text, element.TranslatedText)
                };

                // 确定调整原因
                if (result.AdjustmentRatio < 0.9)
                {
                    result.AdjustmentReason = "翻译后文本较长，缩小字体以适应空间";
                }
                else if (result.AdjustmentRatio > 1.1)
                {
                    result.AdjustmentReason = "翻译后文本较短，放大字体以保持视觉平衡";
                }
                else
                {
                    result.AdjustmentReason = "文本长度变化较小，保持原始字体大小";
                }

                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算字体调整失败: {ElementId}", element.Id);
                
                // 添加默认结果
                results.Add(new FontAdjustmentResult
                {
                    ElementId = element.Id,
                    OriginalFontSize = element.OriginalFontSize,
                    AdjustedFontSize = element.OriginalFontSize,
                    AdjustmentRatio = 1.0,
                    RequiresAdjustment = false,
                    AdjustmentReason = "计算失败，保持原始字体大小",
                    Confidence = 0.0
                });
            }
        }

        _logger.LogInformation("批量字体调整计算完成: {Total} 个元素，{RequireAdjustment} 个需要调整", 
            results.Count, results.Count(r => r.RequiresAdjustment));

        return results;
    }

    public TextSize EstimateTextSize(string text, double fontSize, string fontName)
    {
        if (string.IsNullOrEmpty(text))
        {
            return new TextSize();
        }

        // 简化的文本尺寸估算算法
        var characterCount = text.Length;
        var lineCount = text.Split('\n').Length;
        
        // 估算字符平均宽度（基于字体大小）
        var avgCharWidth = fontSize * 0.6; // 经验值
        
        // 考虑语言特性
        var language = DetectLanguage(text);
        var languageWidthFactor = _languageCharacterWidthRatios.GetValueOrDefault(language, 1.0);
        avgCharWidth *= languageWidthFactor;
        
        // 估算每行平均字符数
        var avgCharsPerLine = characterCount / lineCount;
        
        // 计算尺寸
        var width = avgCharsPerLine * avgCharWidth;
        var height = lineCount * fontSize * 1.2; // 行高通常是字体大小的1.2倍

        return new TextSize
        {
            Width = width,
            Height = height,
            LineCount = lineCount,
            CharacterCount = characterCount
        };
    }

    public FontAdjustmentStrategy GetAdjustmentStrategy(string sourceLanguage, string targetLanguage)
    {
        var strategy = new FontAdjustmentStrategy();

        // 根据语言对调整策略参数
        if (IsAsianLanguage(sourceLanguage) && IsLatinLanguage(targetLanguage))
        {
            // 中文到英文：通常文本会变长
            strategy.MaxScaleDown = 0.6;
            strategy.LengthChangeThreshold = 0.3;
            strategy.LanguageAdjustmentFactor = 0.8;
        }
        else if (IsLatinLanguage(sourceLanguage) && IsAsianLanguage(targetLanguage))
        {
            // 英文到中文：通常文本会变短
            strategy.MaxScaleUp = 1.3;
            strategy.LengthChangeThreshold = 0.3;
            strategy.LanguageAdjustmentFactor = 1.2;
        }
        else if (IsAsianLanguage(sourceLanguage) && IsAsianLanguage(targetLanguage))
        {
            // 亚洲语言之间：变化相对较小
            strategy.MaxScaleDown = 0.8;
            strategy.MaxScaleUp = 1.1;
            strategy.LengthChangeThreshold = 0.15;
        }
        else
        {
            // 拉丁语言之间：使用默认策略
            strategy.LengthChangeThreshold = 0.2;
        }

        return strategy;
    }

    public FontAdjustmentValidation ValidateAdjustment(FontAdjustmentResult adjustment)
    {
        var validation = new FontAdjustmentValidation();

        // 检查字体大小是否在合理范围内
        if (adjustment.AdjustedFontSize < 6)
        {
            validation.Warnings.Add("字体大小过小，可能影响可读性");
            validation.ReadabilityScore -= 2;
        }
        else if (adjustment.AdjustedFontSize > 72)
        {
            validation.Warnings.Add("字体大小过大，可能影响布局");
            validation.ReadabilityScore -= 1;
        }

        // 检查调整比例是否过大
        if (adjustment.AdjustmentRatio < 0.5)
        {
            validation.Warnings.Add("字体缩小幅度过大，建议考虑重新布局");
            validation.ReadabilityScore -= 3;
        }
        else if (adjustment.AdjustmentRatio > 2.0)
        {
            validation.Warnings.Add("字体放大幅度过大，可能超出容器范围");
            validation.ReadabilityScore -= 2;
        }

        // 检查置信度
        if (adjustment.Confidence < 0.5)
        {
            validation.Warnings.Add("字体调整置信度较低，建议手动检查");
            validation.ReadabilityScore -= 1;
        }

        // 提供建议
        if (adjustment.RequiresAdjustment)
        {
            validation.Suggestions.Add("建议在翻译完成后预览效果并进行微调");
        }

        // 计算最终有效性
        validation.IsValid = validation.Errors.Count == 0 && validation.ReadabilityScore >= 5;

        return validation;
    }

    private string DetectLanguage(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return "unknown";
        }

        // 简单的语言检测
        if (Regex.IsMatch(text, @"[\u4e00-\u9fff]"))
        {
            return "zh-CN"; // 中文
        }
        else if (Regex.IsMatch(text, @"[\u3040-\u309f\u30a0-\u30ff]"))
        {
            return "ja"; // 日文
        }
        else if (Regex.IsMatch(text, @"[\uac00-\ud7af]"))
        {
            return "ko"; // 韩文
        }
        else if (Regex.IsMatch(text, @"[\u0600-\u06ff]"))
        {
            return "ar"; // 阿拉伯文
        }
        else if (Regex.IsMatch(text, @"[\u0400-\u04ff]"))
        {
            return "ru"; // 俄文
        }
        else
        {
            return "en"; // 默认英文
        }
    }

    private bool IsAsianLanguage(string language)
    {
        return language switch
        {
            "zh-CN" or "zh-TW" or "ja" or "ko" => true,
            _ => false
        };
    }

    private bool IsLatinLanguage(string language)
    {
        return language switch
        {
            "en" or "fr" or "de" or "es" or "it" or "pt" => true,
            _ => false
        };
    }

    private double CalculateConfidence(string originalText, string translatedText)
    {
        if (string.IsNullOrEmpty(originalText) || string.IsNullOrEmpty(translatedText))
        {
            return 0.0;
        }

        // 基于文本长度比例计算置信度
        var lengthRatio = Math.Min(originalText.Length, translatedText.Length) / 
                         (double)Math.Max(originalText.Length, translatedText.Length);

        // 基于字符类型一致性计算置信度
        var originalLanguage = DetectLanguage(originalText);
        var translatedLanguage = DetectLanguage(translatedText);
        var languageConsistency = originalLanguage != translatedLanguage ? 1.0 : 0.8;

        // 综合置信度
        var confidence = lengthRatio * languageConsistency;
        
        return Math.Max(0.1, Math.Min(1.0, confidence));
    }
}
