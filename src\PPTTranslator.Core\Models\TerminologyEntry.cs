namespace PPTTranslator.Core.Models;

/// <summary>
/// 术语条目模型
/// </summary>
public class TerminologyEntry
{
    /// <summary>
    /// 源语言术语
    /// </summary>
    public string SourceTerm { get; set; } = string.Empty;

    /// <summary>
    /// 目标语言术语
    /// </summary>
    public string TargetTerm { get; set; } = string.Empty;

    /// <summary>
    /// 术语类别
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 术语描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 优先级（数值越大优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;
}

/// <summary>
/// 术语库模型
/// </summary>
public class TerminologyDatabase
{
    /// <summary>
    /// 术语库名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 源语言
    /// </summary>
    public string SourceLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 目标语言
    /// </summary>
    public string TargetLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 术语条目列表
    /// </summary>
    public List<TerminologyEntry> Entries { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = "1.0.0";
}

/// <summary>
/// 术语匹配结果
/// </summary>
public class TerminologyMatch
{
    /// <summary>
    /// 匹配的术语条目
    /// </summary>
    public TerminologyEntry Entry { get; set; } = new();

    /// <summary>
    /// 匹配的起始位置
    /// </summary>
    public int StartIndex { get; set; }

    /// <summary>
    /// 匹配的长度
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    /// 匹配的原始文本
    /// </summary>
    public string MatchedText { get; set; } = string.Empty;

    /// <summary>
    /// 替换后的文本
    /// </summary>
    public string ReplacementText { get; set; } = string.Empty;
}
