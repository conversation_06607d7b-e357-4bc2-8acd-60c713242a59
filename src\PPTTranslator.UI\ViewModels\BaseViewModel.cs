using System.ComponentModel;
using System.Runtime.CompilerServices;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace PPTTranslator.UI.ViewModels;

/// <summary>
/// ViewModel基类
/// </summary>
public abstract class BaseViewModel : ObservableObject
{
    private bool _isBusy;
    private string _statusMessage = string.Empty;
    private string _errorMessage = string.Empty;

    /// <summary>
    /// 是否忙碌
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set
        {
            if (SetProperty(ref _isBusy, value))
            {
                OnPropertyChanged(nameof(IsNotBusy));
                // 通知所有命令重新评估可执行状态
                OnBusyStateChanged();
            }
        }
    }

    /// <summary>
    /// 是否不忙碌
    /// </summary>
    public bool IsNotBusy => !IsBusy;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage
    {
        get => _errorMessage;
        set
        {
            if (SetProperty(ref _errorMessage, value))
            {
                OnPropertyChanged(nameof(HasError));
            }
        }
    }

    /// <summary>
    /// 是否有错误
    /// </summary>
    public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

    /// <summary>
    /// 忙碌状态改变时调用
    /// </summary>
    protected virtual void OnBusyStateChanged()
    {
        // 子类可以重写此方法来处理忙碌状态变化
    }

    /// <summary>
    /// 清除错误消息
    /// </summary>
    public void ClearError()
    {
        ErrorMessage = string.Empty;
    }

    /// <summary>
    /// 设置错误消息
    /// </summary>
    /// <param name="message">错误消息</param>
    public void SetError(string message)
    {
        ErrorMessage = message;
    }

    /// <summary>
    /// 设置状态消息
    /// </summary>
    /// <param name="message">状态消息</param>
    public void SetStatus(string message)
    {
        StatusMessage = message;
    }

    /// <summary>
    /// 执行异步操作的辅助方法
    /// </summary>
    /// <param name="operation">要执行的操作</param>
    /// <param name="statusMessage">状态消息</param>
    /// <param name="showError">是否显示错误</param>
    protected async Task ExecuteAsync(Func<Task> operation, string statusMessage = "", bool showError = true)
    {
        if (IsBusy) return;

        try
        {
            IsBusy = true;
            ClearError();
            
            if (!string.IsNullOrEmpty(statusMessage))
            {
                SetStatus(statusMessage);
            }

            await operation();
        }
        catch (Exception ex)
        {
            if (showError)
            {
                SetError($"操作失败: {ex.Message}");
            }
            
            // 记录日志
            OnError(ex);
        }
        finally
        {
            IsBusy = false;
            if (!string.IsNullOrEmpty(statusMessage))
            {
                SetStatus("就绪");
            }
        }
    }

    /// <summary>
    /// 执行异步操作的辅助方法（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="statusMessage">状态消息</param>
    /// <param name="showError">是否显示错误</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string statusMessage = "", bool showError = true)
    {
        if (IsBusy) return default;

        try
        {
            IsBusy = true;
            ClearError();
            
            if (!string.IsNullOrEmpty(statusMessage))
            {
                SetStatus(statusMessage);
            }

            return await operation();
        }
        catch (Exception ex)
        {
            if (showError)
            {
                SetError($"操作失败: {ex.Message}");
            }
            
            // 记录日志
            OnError(ex);
            return default;
        }
        finally
        {
            IsBusy = false;
            if (!string.IsNullOrEmpty(statusMessage))
            {
                SetStatus("就绪");
            }
        }
    }

    /// <summary>
    /// 错误处理
    /// </summary>
    /// <param name="exception">异常</param>
    protected virtual void OnError(Exception exception)
    {
        // 子类可以重写此方法来处理错误
        System.Diagnostics.Debug.WriteLine($"Error in {GetType().Name}: {exception}");
    }

    /// <summary>
    /// 创建异步命令
    /// </summary>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">可执行条件</param>
    /// <returns>异步命令</returns>
    protected IAsyncRelayCommand CreateAsyncCommand(Func<Task> execute, Func<bool>? canExecute = null)
    {
        return new AsyncRelayCommand(execute, canExecute ?? (() => IsNotBusy));
    }

    /// <summary>
    /// 创建异步命令（带参数）
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">可执行条件</param>
    /// <returns>异步命令</returns>
    protected IAsyncRelayCommand<T> CreateAsyncCommand<T>(Func<T?, Task> execute, Predicate<T?>? canExecute = null)
    {
        return new AsyncRelayCommand<T>(execute, canExecute ?? (_ => IsNotBusy));
    }

    /// <summary>
    /// 创建同步命令
    /// </summary>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">可执行条件</param>
    /// <returns>同步命令</returns>
    protected IRelayCommand CreateCommand(Action execute, Func<bool>? canExecute = null)
    {
        return new RelayCommand(execute, canExecute ?? (() => IsNotBusy));
    }

    /// <summary>
    /// 创建同步命令（带参数）
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">可执行条件</param>
    /// <returns>同步命令</returns>
    protected IRelayCommand<T> CreateCommand<T>(Action<T?> execute, Predicate<T?>? canExecute = null)
    {
        return new RelayCommand<T>(execute, canExecute ?? (_ => IsNotBusy));
    }
}
