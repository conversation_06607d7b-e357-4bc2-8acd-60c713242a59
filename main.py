"""PPT翻译工具主程序入口"""

# 首先设置警告抑制
from src.utils.warning_suppressor import setup_global_warning_filters
setup_global_warning_filters()

from src.core.translator import Translator
from src.ui.gradio_app import TranslatorUI
from src.utils.config import Config


def main():
    """主函数：初始化并启动PPT翻译工具"""
    # 加载配置
    config = Config()

    # 初始化翻译器
    translator = Translator(config)

    # 创建并启动UI
    translator_ui = TranslatorUI(translator)
    translator_ui.create_ui().launch(share=True)


if __name__ == "__main__":
    main()
