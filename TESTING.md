# PPT翻译工具 - 测试指南

## 测试概述

本项目包含完整的单元测试套件，确保各个组件的功能正确性和稳定性。

## 测试结构

```
tests/
└── PPTTranslator.Tests/
    ├── Services/
    │   ├── TerminologyManagerTests.cs      # 术语库管理测试
    │   ├── TextPreprocessorTests.cs        # 文本预处理测试
    │   └── FontAdjustmentServiceTests.cs   # 字体调整测试
    └── PPTTranslator.Tests.csproj
```

## 运行测试

### 命令行运行

```bash
# 运行所有测试
dotnet test

# 运行特定项目的测试
dotnet test tests/PPTTranslator.Tests/PPTTranslator.Tests.csproj

# 运行特定测试类
dotnet test --filter "ClassName=TerminologyManagerTests"

# 运行特定测试方法
dotnet test --filter "MethodName=AddTerminologyEntry_ShouldAddNewEntry"

# 生成测试报告
dotnet test --logger "trx;LogFileName=TestResults.trx"

# 生成代码覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### Visual Studio 中运行

1. 打开 `PPTTranslator.sln`
2. 在"测试资源管理器"中查看所有测试
3. 右键点击测试或测试类，选择"运行测试"

### VS Code 中运行

1. 安装 C# 扩展
2. 使用 `Ctrl+Shift+P` 打开命令面板
3. 输入 "Test: Run All Tests"

## 测试类别

### 1. 术语库管理测试 (TerminologyManagerTests)

测试术语库的核心功能：

- **添加术语**: 验证新术语条目的添加
- **删除术语**: 验证术语条目的删除
- **更新术语**: 验证术语条目的更新
- **搜索术语**: 验证术语搜索功能
- **预处理**: 验证术语预处理和占位符替换
- **后处理**: 验证占位符恢复为目标术语

```bash
# 运行术语库测试
dotnet test --filter "ClassName=TerminologyManagerTests"
```

### 2. 文本预处理测试 (TextPreprocessorTests)

测试文本预处理功能：

- **语言检测**: 验证多语言文本的语言识别
- **文本清理**: 验证多余空格和格式的清理
- **长文本分割**: 验证长文本的智能分割
- **术语集成**: 验证与术语库的集成

```bash
# 运行文本预处理测试
dotnet test --filter "ClassName=TextPreprocessorTests"
```

### 3. 字体调整测试 (FontAdjustmentServiceTests)

测试字体自适应调整功能：

- **字体大小计算**: 验证基于文本长度的字体调整
- **文本尺寸估算**: 验证文本渲染尺寸的估算
- **调整策略**: 验证不同语言对的调整策略
- **批量处理**: 验证批量字体调整
- **结果验证**: 验证调整结果的有效性

```bash
# 运行字体调整测试
dotnet test --filter "ClassName=FontAdjustmentServiceTests"
```

## 测试数据

### 术语库测试数据

测试使用以下术语对：

```json
{
  "人工智能": "Artificial Intelligence",
  "机器学习": "Machine Learning",
  "深度学习": "Deep Learning"
}
```

### 文本测试数据

- **中文文本**: "这是一个测试文本"
- **英文文本**: "This is a test text"
- **混合文本**: "Mixed 中英文 text"
- **长文本**: 超过1000字符的文本用于分割测试

## 模拟对象 (Mocks)

项目使用 Moq 框架创建模拟对象：

```csharp
// 模拟术语库管理器
var mockTerminologyManager = new Mock<ITerminologyManager>();

// 模拟日志记录器
var mockLogger = new Mock<ILogger<ServiceClass>>();
```

## 测试覆盖率

目标测试覆盖率：

- **核心服务**: > 90%
- **业务逻辑**: > 85%
- **工具类**: > 80%

查看覆盖率报告：

```bash
# 生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"

# 安装报告工具
dotnet tool install -g dotnet-reportgenerator-globaltool

# 生成HTML报告
reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coveragereport" -reporttypes:Html
```

## 性能测试

### 基准测试

对关键功能进行性能测试：

```csharp
[Fact]
public void PerformanceTest_TerminologyPreprocessing()
{
    // 测试大量术语的预处理性能
    var stopwatch = Stopwatch.StartNew();
    
    // 执行操作
    var result = _terminologyManager.PreprocessText(largeText, "zh-CN", "en");
    
    stopwatch.Stop();
    
    // 验证性能要求
    Assert.True(stopwatch.ElapsedMilliseconds < 1000); // 1秒内完成
}
```

### 内存测试

```csharp
[Fact]
public void MemoryTest_LargeTerminologyDatabase()
{
    // 测试大型术语库的内存使用
    var initialMemory = GC.GetTotalMemory(true);
    
    // 加载大量术语
    LoadLargeTerminologyDatabase();
    
    var finalMemory = GC.GetTotalMemory(true);
    var memoryUsed = finalMemory - initialMemory;
    
    // 验证内存使用在合理范围内
    Assert.True(memoryUsed < 100 * 1024 * 1024); // 小于100MB
}
```

## 集成测试

### PPT文件测试

创建测试PPT文件进行端到端测试：

```csharp
[Fact]
public async Task IntegrationTest_TranslatePPT()
{
    // 使用真实的PPT文件进行测试
    var testPPTPath = "TestData/sample.pptx";
    var outputPath = "TestOutput/translated.pptx";
    
    var result = await _pptProcessor.TranslatePPTAsync(
        testPPTPath, outputPath, "zh-CN", "en", true);
    
    Assert.True(result.IsSuccess);
    Assert.True(File.Exists(outputPath));
}
```

## 测试最佳实践

### 1. 测试命名

使用描述性的测试方法名：

```csharp
[Fact]
public void MethodName_Scenario_ExpectedBehavior()
{
    // 测试实现
}
```

### 2. AAA 模式

遵循 Arrange-Act-Assert 模式：

```csharp
[Fact]
public void AddTerminologyEntry_WithValidEntry_ShouldAddSuccessfully()
{
    // Arrange
    var entry = new TerminologyEntry { ... };
    
    // Act
    _terminologyManager.AddTerminologyEntry(entry);
    
    // Assert
    var entries = _terminologyManager.GetAllEntries();
    Assert.Contains(entries, e => e.SourceTerm == entry.SourceTerm);
}
```

### 3. 参数化测试

使用 Theory 和 InlineData 进行参数化测试：

```csharp
[Theory]
[InlineData("Hello World", "en")]
[InlineData("你好世界", "zh-CN")]
[InlineData("こんにちは", "ja")]
public void DetectLanguage_ShouldReturnCorrectLanguage(string text, string expected)
{
    var result = _textPreprocessor.DetectLanguage(text);
    Assert.Equal(expected, result);
}
```

## 持续集成

### GitHub Actions 配置

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal
```

## 故障排除

### 常见问题

1. **测试失败**: 检查依赖项是否正确安装
2. **模拟对象错误**: 确保 Moq 版本兼容
3. **文件路径问题**: 使用相对路径或环境变量

### 调试测试

```csharp
[Fact]
public void DebugTest()
{
    // 使用调试器断点
    System.Diagnostics.Debugger.Break();
    
    // 或输出调试信息
    _testOutputHelper.WriteLine("Debug information");
}
```

## 测试报告

生成详细的测试报告：

```bash
# XML 格式报告
dotnet test --logger "trx;LogFileName=TestResults.trx"

# JUnit 格式报告
dotnet test --logger "junit;LogFileName=TestResults.xml"

# HTML 报告 (需要额外工具)
dotnet test --logger html
```
