using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;
using PPTTranslator.Core.Services;
using PPTTranslator.UI.Views;

namespace PPTTranslator.UI.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public class MainViewModel : BaseViewModel
{
    private readonly IPPTProcessor _pptProcessor;
    private readonly ITranslationService _translationService;
    private readonly ConfigurationService _configService;
    private readonly ILogger<MainViewModel> _logger;
    private readonly IServiceProvider _serviceProvider;

    private string _selectedFilePath = string.Empty;
    private string _outputFilePath = string.Empty;
    private string _sourceLanguage = "zh-CN";
    private string _targetLanguage = "en";
    private bool _useTerminology = true;
    private string _selectedModel = "glm-4-flash";
    private double _progressValue;
    private string _progressText = string.Empty;
    private PPTInfo? _pptInfo;

    public MainViewModel(
        IPPTProcessor pptProcessor,
        ITranslationService translationService,
        ConfigurationService configService,
        ILogger<MainViewModel> logger,
        IServiceProvider serviceProvider)
    {
        _pptProcessor = pptProcessor;
        _translationService = translationService;
        _configService = configService;
        _logger = logger;
        _serviceProvider = serviceProvider;

        // 初始化命令
        SelectFileCommand = CreateAsyncCommand(SelectFileAsync);
        TranslateCommand = CreateAsyncCommand(TranslateAsync, () => CanTranslate);
        OpenOutputFolderCommand = CreateCommand(OpenOutputFolder, () => !string.IsNullOrEmpty(OutputFilePath));
        ValidateConnectionCommand = CreateAsyncCommand(ValidateConnectionAsync);
        ManageTerminologyCommand = CreateCommand(ManageTerminology);

        // 初始化集合
        SupportedLanguages = new ObservableCollection<SupportedLanguage>();
        AvailableModels = new ObservableCollection<string>();
        TranslationResults = new ObservableCollection<PPTTextElement>();

        // 加载初始数据
        LoadInitialData();
    }

    #region 属性

    /// <summary>
    /// 选中的文件路径
    /// </summary>
    public string SelectedFilePath
    {
        get => _selectedFilePath;
        set
        {
            if (SetProperty(ref _selectedFilePath, value))
            {
                OnPropertyChanged(nameof(CanTranslate));
                OnPropertyChanged(nameof(HasSelectedFile));
                if (!string.IsNullOrEmpty(value))
                {
                    _ = LoadPPTInfoAsync(value);
                }
            }
        }
    }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string OutputFilePath
    {
        get => _outputFilePath;
        set => SetProperty(ref _outputFilePath, value);
    }

    /// <summary>
    /// 源语言
    /// </summary>
    public string SourceLanguage
    {
        get => _sourceLanguage;
        set => SetProperty(ref _sourceLanguage, value);
    }

    /// <summary>
    /// 目标语言
    /// </summary>
    public string TargetLanguage
    {
        get => _targetLanguage;
        set => SetProperty(ref _targetLanguage, value);
    }

    /// <summary>
    /// 是否使用术语库
    /// </summary>
    public bool UseTerminology
    {
        get => _useTerminology;
        set => SetProperty(ref _useTerminology, value);
    }

    /// <summary>
    /// 选中的模型
    /// </summary>
    public string SelectedModel
    {
        get => _selectedModel;
        set => SetProperty(ref _selectedModel, value);
    }

    /// <summary>
    /// 进度值
    /// </summary>
    public double ProgressValue
    {
        get => _progressValue;
        set => SetProperty(ref _progressValue, value);
    }

    /// <summary>
    /// 进度文本
    /// </summary>
    public string ProgressText
    {
        get => _progressText;
        set => SetProperty(ref _progressText, value);
    }

    /// <summary>
    /// PPT信息
    /// </summary>
    public PPTInfo? PPTInfo
    {
        get => _pptInfo;
        set => SetProperty(ref _pptInfo, value);
    }

    /// <summary>
    /// 是否有选中的文件
    /// </summary>
    public bool HasSelectedFile => !string.IsNullOrEmpty(SelectedFilePath);

    /// <summary>
    /// 是否可以翻译
    /// </summary>
    public bool CanTranslate => HasSelectedFile && IsNotBusy;

    /// <summary>
    /// 支持的语言列表
    /// </summary>
    public ObservableCollection<SupportedLanguage> SupportedLanguages { get; }

    /// <summary>
    /// 可用模型列表
    /// </summary>
    public ObservableCollection<string> AvailableModels { get; }

    /// <summary>
    /// 翻译结果
    /// </summary>
    public ObservableCollection<PPTTextElement> TranslationResults { get; }

    #endregion

    #region 命令

    /// <summary>
    /// 选择文件命令
    /// </summary>
    public IAsyncRelayCommand SelectFileCommand { get; }

    /// <summary>
    /// 翻译命令
    /// </summary>
    public IAsyncRelayCommand TranslateCommand { get; }

    /// <summary>
    /// 打开输出文件夹命令
    /// </summary>
    public IRelayCommand OpenOutputFolderCommand { get; }

    /// <summary>
    /// 验证连接命令
    /// </summary>
    public IAsyncRelayCommand ValidateConnectionCommand { get; }

    /// <summary>
    /// 管理术语库命令
    /// </summary>
    public IRelayCommand ManageTerminologyCommand { get; }

    #endregion

    #region 方法

    /// <summary>
    /// 选择文件
    /// </summary>
    private Task SelectFileAsync()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "选择PPT文件",
            Filter = "PowerPoint文件|*.pptx;*.ppt|所有文件|*.*",
            CheckFileExists = true
        };

        if (openFileDialog.ShowDialog() == true)
        {
            SelectedFilePath = openFileDialog.FileName;
            
            // 生成输出文件路径
            var directory = Path.GetDirectoryName(SelectedFilePath);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(SelectedFilePath);
            var extension = Path.GetExtension(SelectedFilePath);
            OutputFilePath = Path.Combine(directory!, $"{fileNameWithoutExtension}_translated{extension}");

            SetStatus("文件选择完成");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 翻译PPT
    /// </summary>
    private async Task TranslateAsync()
    {
        if (string.IsNullOrEmpty(SelectedFilePath))
        {
            SetError("请先选择PPT文件");
            return;
        }

        try
        {
            // 验证文件
            var validationResult = await _pptProcessor.ValidatePPTAsync(SelectedFilePath);
            if (!validationResult.IsValid)
            {
                SetError($"文件验证失败: {string.Join(", ", validationResult.Errors)}");
                return;
            }

            // 创建进度报告器
            var progress = new Progress<TranslationProgress>(OnTranslationProgress);

            // 执行翻译
            var result = await _pptProcessor.TranslatePPTAsync(
                SelectedFilePath,
                OutputFilePath,
                SourceLanguage,
                TargetLanguage,
                UseTerminology,
                progress);

            if (result.IsSuccess)
            {
                // 更新翻译结果
                TranslationResults.Clear();
                foreach (var element in result.TranslatedElements)
                {
                    TranslationResults.Add(element);
                }

                SetStatus($"翻译完成！处理了 {result.ProcessedTextCount} 个文本元素，耗时 {result.ElapsedMilliseconds / 1000.0:F1} 秒");
                
                // 询问是否打开输出文件
                var messageResult = MessageBox.Show(
                    "翻译完成！是否打开输出文件？",
                    "翻译完成",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (messageResult == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = OutputFilePath,
                        UseShellExecute = true
                    });
                }
            }
            else
            {
                SetError($"翻译失败: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "翻译过程中发生错误");
            SetError($"翻译失败: {ex.Message}");
        }
        finally
        {
            ProgressValue = 0;
            ProgressText = string.Empty;
        }
    }

    /// <summary>
    /// 打开输出文件夹
    /// </summary>
    private void OpenOutputFolder()
    {
        if (!string.IsNullOrEmpty(OutputFilePath))
        {
            var directory = Path.GetDirectoryName(OutputFilePath);
            if (!string.IsNullOrEmpty(directory) && Directory.Exists(directory))
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = directory,
                    UseShellExecute = true
                });
            }
        }
    }

    /// <summary>
    /// 验证连接
    /// </summary>
    private async Task ValidateConnectionAsync()
    {
        var isValid = await _translationService.ValidateConnectionAsync();

        if (isValid)
        {
            SetStatus("连接验证成功");
            MessageBox.Show("翻译服务连接正常", "连接验证", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            SetError("翻译服务连接失败，请检查配置");
            MessageBox.Show("翻译服务连接失败，请检查配置", "连接验证", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 管理术语库
    /// </summary>
    private void ManageTerminology()
    {
        try
        {
            var terminologyViewModel = _serviceProvider.GetRequiredService<TerminologyViewModel>();
            var terminologyWindow = new TerminologyWindow(terminologyViewModel);
            terminologyWindow.Owner = Application.Current.MainWindow;
            terminologyWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开术语库管理窗口失败");
            SetError($"打开术语库管理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载PPT信息
    /// </summary>
    private async Task LoadPPTInfoAsync(string filePath)
    {
        try
        {
            PPTInfo = await _pptProcessor.GetPPTInfoAsync(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载PPT信息失败: {FilePath}", filePath);
            PPTInfo = null;
        }
    }

    /// <summary>
    /// 翻译进度回调
    /// </summary>
    private void OnTranslationProgress(TranslationProgress progress)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            ProgressValue = progress.ProgressPercentage;
            ProgressText = progress.StatusMessage;
            
            if (progress.HasError)
            {
                SetError(progress.ErrorMessage);
            }
            else if (!string.IsNullOrEmpty(progress.StatusMessage))
            {
                SetStatus(progress.StatusMessage);
            }
        });
    }

    /// <summary>
    /// 加载初始数据
    /// </summary>
    private async void LoadInitialData()
    {
        try
        {
            // 加载支持的语言
            var languages = await _translationService.GetSupportedLanguagesAsync();
            foreach (var language in languages)
            {
                SupportedLanguages.Add(language);
            }

            // 加载可用模型
            var serviceInfo = await _translationService.GetServiceInfoAsync();
            foreach (var model in serviceInfo.SupportedModels)
            {
                AvailableModels.Add(model);
            }

            // 从配置加载默认值
            var config = _configService.Config;
            SourceLanguage = config.Translation.DefaultSourceLanguage;
            TargetLanguage = config.Translation.DefaultTargetLanguage;
            UseTerminology = config.Translation.UseTerminologyByDefault;
            SelectedModel = config.Translation.DefaultModel;

            SetStatus("就绪");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载初始数据失败");
            SetError("初始化失败，请检查配置");
        }
    }

    /// <summary>
    /// 忙碌状态改变时的处理
    /// </summary>
    protected override void OnBusyStateChanged()
    {
        base.OnBusyStateChanged();
        OnPropertyChanged(nameof(CanTranslate));
    }

    #endregion
}
