using System.Text.Json;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Configuration;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 配置服务
/// </summary>
public class ConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private AppConfig _config;

    public ConfigurationService(ILogger<ConfigurationService> logger, string configFilePath = "appsettings.json")
    {
        _logger = logger;
        _configFilePath = configFilePath;
        _config = LoadConfiguration();
    }

    /// <summary>
    /// 获取当前配置
    /// </summary>
    public AppConfig Config => _config;

    /// <summary>
    /// 加载配置
    /// </summary>
    public AppConfig LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configFilePath))
            {
                var jsonContent = File.ReadAllText(_configFilePath);
                var config = JsonSerializer.Deserialize<AppConfig>(jsonContent);
                
                if (config != null)
                {
                    _config = config;
                    _logger.LogInformation("配置文件加载成功: {ConfigPath}", _configFilePath);
                    return config;
                }
            }

            // 如果文件不存在或解析失败，创建默认配置
            _config = CreateDefaultConfiguration();
            SaveConfiguration();
            _logger.LogInformation("创建默认配置文件: {ConfigPath}", _configFilePath);
            
            return _config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置文件失败，使用默认配置: {ConfigPath}", _configFilePath);
            _config = CreateDefaultConfiguration();
            return _config;
        }
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    public async Task SaveConfigurationAsync()
    {
        try
        {
            var directory = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var jsonContent = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(_configFilePath, jsonContent);
            _logger.LogInformation("配置文件保存成功: {ConfigPath}", _configFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置文件失败: {ConfigPath}", _configFilePath);
            throw;
        }
    }

    /// <summary>
    /// 同步保存配置
    /// </summary>
    public void SaveConfiguration()
    {
        try
        {
            var directory = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var jsonContent = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            File.WriteAllText(_configFilePath, jsonContent);
            _logger.LogInformation("配置文件保存成功: {ConfigPath}", _configFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置文件失败: {ConfigPath}", _configFilePath);
            throw;
        }
    }

    /// <summary>
    /// 更新翻译配置
    /// </summary>
    public void UpdateTranslationConfig(Action<TranslationConfig> updateAction)
    {
        updateAction(_config.Translation);
        SaveConfiguration();
    }

    /// <summary>
    /// 更新术语库配置
    /// </summary>
    public void UpdateTerminologyConfig(Action<TerminologyConfig> updateAction)
    {
        updateAction(_config.Terminology);
        SaveConfiguration();
    }

    /// <summary>
    /// 更新字体调整配置
    /// </summary>
    public void UpdateFontAdjustmentConfig(Action<FontAdjustmentConfig> updateAction)
    {
        updateAction(_config.FontAdjustment);
        SaveConfiguration();
    }

    /// <summary>
    /// 更新UI配置
    /// </summary>
    public void UpdateUIConfig(Action<UIConfig> updateAction)
    {
        updateAction(_config.UI);
        SaveConfiguration();
    }

    /// <summary>
    /// 重置为默认配置
    /// </summary>
    public void ResetToDefault()
    {
        _config = CreateDefaultConfiguration();
        SaveConfiguration();
        _logger.LogInformation("配置已重置为默认值");
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    public List<string> ValidateConfiguration()
    {
        var errors = new List<string>();

        // 验证翻译配置
        if (string.IsNullOrEmpty(_config.Translation.DefaultSourceLanguage))
        {
            errors.Add("默认源语言不能为空");
        }

        if (string.IsNullOrEmpty(_config.Translation.DefaultTargetLanguage))
        {
            errors.Add("默认目标语言不能为空");
        }

        if (_config.Translation.TimeoutSeconds <= 0)
        {
            errors.Add("请求超时时间必须大于0");
        }

        if (_config.Translation.RetryCount < 0)
        {
            errors.Add("重试次数不能为负数");
        }

        // 验证字体调整配置
        if (_config.FontAdjustment.MinFontSize <= 0)
        {
            errors.Add("最小字体大小必须大于0");
        }

        if (_config.FontAdjustment.MaxFontSize <= _config.FontAdjustment.MinFontSize)
        {
            errors.Add("最大字体大小必须大于最小字体大小");
        }

        if (_config.FontAdjustment.MaxScaleDown <= 0 || _config.FontAdjustment.MaxScaleDown > 1)
        {
            errors.Add("最大缩放比例必须在0-1之间");
        }

        if (_config.FontAdjustment.MaxScaleUp < 1)
        {
            errors.Add("最大放大比例必须大于等于1");
        }

        // 验证术语库配置
        if (_config.Terminology.FuzzyMatchThreshold < 0 || _config.Terminology.FuzzyMatchThreshold > 1)
        {
            errors.Add("模糊匹配阈值必须在0-1之间");
        }

        if (_config.Terminology.BackupRetentionDays <= 0)
        {
            errors.Add("备份保留天数必须大于0");
        }

        // 验证UI配置
        if (_config.UI.WindowWidth <= 0 || _config.UI.WindowHeight <= 0)
        {
            errors.Add("窗口尺寸必须大于0");
        }

        if (_config.UI.AutoSaveIntervalMinutes <= 0)
        {
            errors.Add("自动保存间隔必须大于0");
        }

        return errors;
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    private AppConfig CreateDefaultConfiguration()
    {
        return new AppConfig
        {
            Translation = new TranslationConfig
            {
                DefaultSourceLanguage = "zh-CN",
                DefaultTargetLanguage = "en",
                UseTerminologyByDefault = true,
                Provider = "ZhipuAI",
                ApiKey = "",
                ServerUrl = "http://localhost:11434",
                DefaultModel = "glm-4-flash",
                TimeoutSeconds = 30,
                RetryCount = 3,
                BatchConcurrency = 5,
                EnableCache = true,
                CacheExpirationHours = 24
            },
            Terminology = new TerminologyConfig
            {
                DefaultTerminologyPath = "Data/terminology.json",
                TerminologyDirectory = "Data/Terminologies",
                EnablePreprocessing = true,
                MatchingMode = "Smart",
                FuzzyMatchThreshold = 0.8,
                CaseSensitive = false,
                AutoBackup = true,
                BackupRetentionDays = 30
            },
            FontAdjustment = new FontAdjustmentConfig
            {
                EnableAutoAdjustment = true,
                MinFontSize = 8.0,
                MaxFontSize = 72.0,
                MaxScaleDown = 0.7,
                MaxScaleUp = 1.2,
                AdjustmentThreshold = 0.2,
                ConsiderCharacterWidth = true,
                AdjustmentAlgorithm = "Advanced"
            },
            Logging = new LoggingConfig
            {
                LogLevel = "Information",
                LogFilePath = "Logs/app.log",
                EnableConsoleLogging = true,
                EnableFileLogging = true,
                MaxFileSizeMB = 10,
                RetainedFileCount = 5
            },
            UI = new UIConfig
            {
                DefaultTheme = "Light",
                DefaultLanguage = "zh-CN",
                WindowWidth = 1200,
                WindowHeight = 800,
                RememberWindowPosition = true,
                ShowProgressDetails = true,
                AutoSaveIntervalMinutes = 5,
                RecentFilesCount = 10
            }
        };
    }
}
