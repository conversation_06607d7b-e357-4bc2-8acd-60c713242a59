using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Services;
using PPTTranslator.Core.Models;

class Program
{
    static async Task Main(string[] args)
    {
        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<TerminologyManager>();

        // 创建术语库管理器
        var terminologyManager = new TerminologyManager(logger);

        Console.WriteLine("=== 术语库功能测试 ===");

        // 测试添加术语
        Console.WriteLine("\n1. 测试添加术语...");
        var entry1 = new TerminologyEntry
        {
            SourceTerm = "人工智能",
            TargetTerm = "Artificial Intelligence",
            Category = "技术",
            Description = "AI技术相关术语",
            Priority = 10
        };

        var entry2 = new TerminologyEntry
        {
            SourceTerm = "机器学习",
            TargetTerm = "Machine Learning",
            Category = "技术",
            Description = "ML技术相关术语",
            Priority = 9
        };

        terminologyManager.AddTerminologyEntry(entry1);
        terminologyManager.AddTerminologyEntry(entry2);

        Console.WriteLine($"添加了 {terminologyManager.GetAllEntries().Count()} 条术语");

        // 测试搜索
        Console.WriteLine("\n2. 测试搜索功能...");
        var searchResults = terminologyManager.SearchEntries("人工");
        Console.WriteLine($"搜索'人工'找到 {searchResults.Count()} 条结果");

        // 测试导出CSV
        Console.WriteLine("\n3. 测试导出CSV...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("test_terminology.csv", "csv");
            Console.WriteLine("CSV导出成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"CSV导出失败: {ex.Message}");
        }

        // 测试导出JSON
        Console.WriteLine("\n4. 测试导出JSON...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("test_terminology.json", "json");
            Console.WriteLine("JSON导出成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"JSON导出失败: {ex.Message}");
        }

        // 测试导出Excel
        Console.WriteLine("\n5. 测试导出Excel...");
        try
        {
            await terminologyManager.ExportTerminologyAsync("test_terminology.xlsx", "xlsx");
            Console.WriteLine("Excel导出成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Excel导出失败: {ex.Message}");
        }

        Console.WriteLine("\n=== 测试完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
