# PPT翻译工具 - 开发环境设置脚本

param(
    [switch]$SkipDotNetCheck = $false,
    [switch]$SkipVSCheck = $false,
    [switch]$InstallTools = $false
)

Write-Host "PPT翻译工具 - 开发环境设置" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# 检查 PowerShell 版本
$PSVersion = $PSVersionTable.PSVersion
Write-Host "PowerShell 版本: $PSVersion" -ForegroundColor Yellow

if ($PSVersion.Major -lt 5) {
    Write-Warning "建议使用 PowerShell 5.0 或更高版本"
}

# 检查 .NET SDK
if (!$SkipDotNetCheck) {
    Write-Host "检查 .NET SDK..." -ForegroundColor Yellow
    
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($dotnetVersion) {
            Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
            
            # 检查是否为 .NET 8.0
            if ($dotnetVersion -like "8.*") {
                Write-Host "✓ .NET 8.0 SDK 已安装" -ForegroundColor Green
            } else {
                Write-Warning ".NET 8.0 SDK 未找到，当前版本: $dotnetVersion"
                Write-Host "请安装 .NET 8.0 SDK: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Red
            }
        } else {
            Write-Host "✗ .NET SDK 未安装" -ForegroundColor Red
            Write-Host "请安装 .NET 8.0 SDK: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ 无法检查 .NET SDK" -ForegroundColor Red
    }
}

# 检查 Visual Studio 或 VS Code
if (!$SkipVSCheck) {
    Write-Host "检查开发工具..." -ForegroundColor Yellow
    
    # 检查 Visual Studio
    $vsInstallations = @()
    
    # 检查 VS 2022
    $vs2022Path = "${env:ProgramFiles}\Microsoft Visual Studio\2022"
    if (Test-Path $vs2022Path) {
        $editions = Get-ChildItem $vs2022Path -Directory | Where-Object { $_.Name -in @("Enterprise", "Professional", "Community") }
        foreach ($edition in $editions) {
            $vsInstallations += "Visual Studio 2022 $($edition.Name)"
        }
    }
    
    # 检查 VS Code
    $vsCodePaths = @(
        "${env:ProgramFiles}\Microsoft VS Code\Code.exe",
        "${env:ProgramFiles(x86)}\Microsoft VS Code\Code.exe",
        "${env:LOCALAPPDATA}\Programs\Microsoft VS Code\Code.exe"
    )
    
    $vsCodeInstalled = $false
    foreach ($path in $vsCodePaths) {
        if (Test-Path $path) {
            $vsCodeInstalled = $true
            break
        }
    }
    
    if ($vsInstallations.Count -gt 0) {
        Write-Host "✓ 找到 Visual Studio:" -ForegroundColor Green
        foreach ($vs in $vsInstallations) {
            Write-Host "  - $vs" -ForegroundColor Gray
        }
    }
    
    if ($vsCodeInstalled) {
        Write-Host "✓ Visual Studio Code 已安装" -ForegroundColor Green
    }
    
    if ($vsInstallations.Count -eq 0 -and !$vsCodeInstalled) {
        Write-Host "✗ 未找到 Visual Studio 或 VS Code" -ForegroundColor Red
        Write-Host "建议安装以下开发工具之一:" -ForegroundColor Yellow
        Write-Host "  - Visual Studio 2022 Community (免费): https://visualstudio.microsoft.com/vs/community/" -ForegroundColor Gray
        Write-Host "  - Visual Studio Code: https://code.visualstudio.com/" -ForegroundColor Gray
    }
}

# 检查 Git
Write-Host "检查 Git..." -ForegroundColor Yellow
try {
    $gitVersion = git --version 2>$null
    if ($gitVersion) {
        Write-Host "✓ $gitVersion" -ForegroundColor Green
    } else {
        Write-Host "✗ Git 未安装" -ForegroundColor Red
        Write-Host "请安装 Git: https://git-scm.com/download/windows" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Git 未安装" -ForegroundColor Red
}

# 安装开发工具 (可选)
if ($InstallTools) {
    Write-Host "安装开发工具..." -ForegroundColor Yellow
    
    # 检查是否有 winget
    try {
        winget --version >$null 2>&1
        $hasWinget = $true
    } catch {
        $hasWinget = $false
    }
    
    if ($hasWinget) {
        Write-Host "使用 winget 安装工具..." -ForegroundColor Yellow
        
        # 安装 .NET SDK
        Write-Host "安装 .NET 8.0 SDK..." -ForegroundColor Gray
        winget install Microsoft.DotNet.SDK.8
        
        # 安装 VS Code
        Write-Host "安装 Visual Studio Code..." -ForegroundColor Gray
        winget install Microsoft.VisualStudioCode
        
        # 安装 Git
        Write-Host "安装 Git..." -ForegroundColor Gray
        winget install Git.Git
        
    } else {
        Write-Host "winget 不可用，请手动安装以下工具:" -ForegroundColor Yellow
        Write-Host "1. .NET 8.0 SDK: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Gray
        Write-Host "2. Visual Studio Code: https://code.visualstudio.com/" -ForegroundColor Gray
        Write-Host "3. Git: https://git-scm.com/download/windows" -ForegroundColor Gray
    }
}

# 检查项目依赖
Write-Host "检查项目依赖..." -ForegroundColor Yellow

$projectRoot = $PSScriptRoot
$solutionFile = Join-Path $projectRoot "PPTTranslator.sln"

if (Test-Path $solutionFile) {
    Write-Host "✓ 找到解决方案文件" -ForegroundColor Green
    
    try {
        Write-Host "还原 NuGet 包..." -ForegroundColor Gray
        dotnet restore $solutionFile --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ NuGet 包还原成功" -ForegroundColor Green
        } else {
            Write-Host "✗ NuGet 包还原失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ 无法还原 NuGet 包" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 未找到解决方案文件" -ForegroundColor Red
}

# 创建开发配置文件
Write-Host "创建开发配置..." -ForegroundColor Yellow

$devConfigPath = Join-Path $projectRoot "appsettings.Development.json"
if (!(Test-Path $devConfigPath)) {
    $devConfig = @{
        "Translation" = @{
            "DefaultSourceLanguage" = "zh-CN"
            "DefaultTargetLanguage" = "en"
            "UseTerminologyByDefault" = $true
            "Provider" = "ZhipuAI"
            "ApiKey" = ""
            "ServerUrl" = "http://localhost:11434"
            "DefaultModel" = "glm-4-flash"
            "TimeoutSeconds" = 30
            "RetryCount" = 3
            "EnableCache" = $true
        }
        "Logging" = @{
            "LogLevel" = "Debug"
            "EnableConsoleLogging" = $true
            "EnableFileLogging" = $true
        }
    }
    
    $devConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $devConfigPath -Encoding UTF8
    Write-Host "✓ 创建开发配置文件: appsettings.Development.json" -ForegroundColor Green
} else {
    Write-Host "✓ 开发配置文件已存在" -ForegroundColor Green
}

# 创建 VS Code 配置
$vscodeDir = Join-Path $projectRoot ".vscode"
if (!(Test-Path $vscodeDir)) {
    New-Item -ItemType Directory -Path $vscodeDir -Force | Out-Null
}

# launch.json
$launchJsonPath = Join-Path $vscodeDir "launch.json"
if (!(Test-Path $launchJsonPath)) {
    $launchConfig = @{
        "version" = "0.2.0"
        "configurations" = @(
            @{
                "name" = "Launch PPT Translator"
                "type" = "coreclr"
                "request" = "launch"
                "program" = "`${workspaceFolder}/src/PPTTranslator.UI/bin/Debug/net8.0-windows/PPTTranslator.UI.exe"
                "args" = @()
                "cwd" = "`${workspaceFolder}"
                "stopAtEntry" = $false
                "console" = "internalConsole"
            }
        )
    }
    
    $launchConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $launchJsonPath -Encoding UTF8
    Write-Host "✓ 创建 VS Code launch.json" -ForegroundColor Green
}

# tasks.json
$tasksJsonPath = Join-Path $vscodeDir "tasks.json"
if (!(Test-Path $tasksJsonPath)) {
    $tasksConfig = @{
        "version" = "2.0.0"
        "tasks" = @(
            @{
                "label" = "build"
                "command" = "dotnet"
                "type" = "process"
                "args" = @("build", "`${workspaceFolder}/PPTTranslator.sln")
                "group" = "build"
                "presentation" = @{
                    "reveal" = "silent"
                }
                "problemMatcher" = "`$msCompile"
            },
            @{
                "label" = "test"
                "command" = "dotnet"
                "type" = "process"
                "args" = @("test", "`${workspaceFolder}/tests/PPTTranslator.Tests/PPTTranslator.Tests.csproj")
                "group" = "test"
                "presentation" = @{
                    "reveal" = "always"
                }
            }
        )
    }
    
    $tasksConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $tasksJsonPath -Encoding UTF8
    Write-Host "✓ 创建 VS Code tasks.json" -ForegroundColor Green
}

Write-Host ""
Write-Host "开发环境设置完成!" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Yellow
Write-Host "1. 编辑 appsettings.json 或 appsettings.Development.json 配置翻译服务API密钥" -ForegroundColor Gray
Write-Host "2. 运行 'dotnet build' 构建项目" -ForegroundColor Gray
Write-Host "3. 运行 'dotnet test' 执行测试" -ForegroundColor Gray
Write-Host "4. 运行 'dotnet run --project src/PPTTranslator.UI' 启动应用" -ForegroundColor Gray
Write-Host ""
Write-Host "或者使用构建脚本:" -ForegroundColor Yellow
Write-Host "  .\build.bat" -ForegroundColor Gray
Write-Host ""
