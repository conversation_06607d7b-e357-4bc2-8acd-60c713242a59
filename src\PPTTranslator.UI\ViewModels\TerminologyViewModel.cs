using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;

namespace PPTTranslator.UI.ViewModels;

/// <summary>
/// 术语库视图模型
/// </summary>
public class TerminologyViewModel : BaseViewModel
{
    private readonly ITerminologyManager _terminologyManager;
    private readonly ILogger<TerminologyViewModel> _logger;

    private string _searchKeyword = string.Empty;
    private TerminologyEntry? _selectedEntry;
    private string _newSourceTerm = string.Empty;
    private string _newTargetTerm = string.Empty;
    private string _newCategory = string.Empty;
    private string _newDescription = string.Empty;
    private int _newPriority = 5;
    private bool _isEditMode = false;

    public TerminologyViewModel(
        ITerminologyManager terminologyManager,
        ILogger<TerminologyViewModel> logger)
    {
        _terminologyManager = terminologyManager;
        _logger = logger;

        // 初始化集合
        TerminologyEntries = new ObservableCollection<TerminologyEntry>();
        FilteredEntries = new ObservableCollection<TerminologyEntry>();
        Categories = new ObservableCollection<string> { "技术", "业务", "通用", "专业" };

        // 初始化命令
        AddEntryCommand = CreateCommand(AddEntry, () => CanAddEntry);
        UpdateEntryCommand = CreateCommand(UpdateEntry, () => CanUpdateEntry);
        DeleteEntryCommand = CreateCommand(DeleteEntry, () => CanDeleteEntry);
        ClearFormCommand = CreateCommand(ClearForm);
        SearchCommand = CreateCommand(SearchEntries);
        ImportCommand = CreateAsyncCommand(ImportTerminologyAsync);
        ExportCommand = CreateAsyncCommand(ExportTerminologyAsync);
        ClearAllCommand = CreateCommand(ClearAllEntries);
        RefreshCommand = CreateCommand(RefreshEntries);

        // 加载数据
        LoadTerminologyEntries();
    }

    #region 属性

    /// <summary>
    /// 术语条目集合
    /// </summary>
    public ObservableCollection<TerminologyEntry> TerminologyEntries { get; }

    /// <summary>
    /// 过滤后的术语条目
    /// </summary>
    public ObservableCollection<TerminologyEntry> FilteredEntries { get; }

    /// <summary>
    /// 类别集合
    /// </summary>
    public ObservableCollection<string> Categories { get; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string SearchKeyword
    {
        get => _searchKeyword;
        set
        {
            if (SetProperty(ref _searchKeyword, value))
            {
                SearchEntries();
            }
        }
    }

    /// <summary>
    /// 选中的术语条目
    /// </summary>
    public TerminologyEntry? SelectedEntry
    {
        get => _selectedEntry;
        set
        {
            if (SetProperty(ref _selectedEntry, value))
            {
                LoadSelectedEntry();
                OnPropertyChanged(nameof(CanDeleteEntry));
                OnPropertyChanged(nameof(CanUpdateEntry));
            }
        }
    }

    /// <summary>
    /// 新源术语
    /// </summary>
    public string NewSourceTerm
    {
        get => _newSourceTerm;
        set
        {
            if (SetProperty(ref _newSourceTerm, value))
            {
                OnPropertyChanged(nameof(CanAddEntry));
                OnPropertyChanged(nameof(CanUpdateEntry));
            }
        }
    }

    /// <summary>
    /// 新目标术语
    /// </summary>
    public string NewTargetTerm
    {
        get => _newTargetTerm;
        set
        {
            if (SetProperty(ref _newTargetTerm, value))
            {
                OnPropertyChanged(nameof(CanAddEntry));
                OnPropertyChanged(nameof(CanUpdateEntry));
            }
        }
    }

    /// <summary>
    /// 新类别
    /// </summary>
    public string NewCategory
    {
        get => _newCategory;
        set => SetProperty(ref _newCategory, value);
    }

    /// <summary>
    /// 新描述
    /// </summary>
    public string NewDescription
    {
        get => _newDescription;
        set => SetProperty(ref _newDescription, value);
    }

    /// <summary>
    /// 新优先级
    /// </summary>
    public int NewPriority
    {
        get => _newPriority;
        set => SetProperty(ref _newPriority, value);
    }

    /// <summary>
    /// 是否为编辑模式
    /// </summary>
    public bool IsEditMode
    {
        get => _isEditMode;
        set => SetProperty(ref _isEditMode, value);
    }

    /// <summary>
    /// 是否可以添加条目
    /// </summary>
    public bool CanAddEntry => !string.IsNullOrWhiteSpace(NewSourceTerm) &&
                               !string.IsNullOrWhiteSpace(NewTargetTerm) &&
                               !IsEditMode;

    /// <summary>
    /// 是否可以更新条目
    /// </summary>
    public bool CanUpdateEntry => !string.IsNullOrWhiteSpace(NewSourceTerm) &&
                                  !string.IsNullOrWhiteSpace(NewTargetTerm) &&
                                  IsEditMode &&
                                  SelectedEntry != null;

    /// <summary>
    /// 是否可以删除条目
    /// </summary>
    public bool CanDeleteEntry => SelectedEntry != null;

    #endregion

    #region 命令

    /// <summary>
    /// 添加条目命令
    /// </summary>
    public IRelayCommand AddEntryCommand { get; }

    /// <summary>
    /// 更新条目命令
    /// </summary>
    public IRelayCommand UpdateEntryCommand { get; }

    /// <summary>
    /// 删除条目命令
    /// </summary>
    public IRelayCommand DeleteEntryCommand { get; }

    /// <summary>
    /// 清空表单命令
    /// </summary>
    public IRelayCommand ClearFormCommand { get; }

    /// <summary>
    /// 搜索命令
    /// </summary>
    public IRelayCommand SearchCommand { get; }

    /// <summary>
    /// 导入命令
    /// </summary>
    public IAsyncRelayCommand ImportCommand { get; }

    /// <summary>
    /// 导出命令
    /// </summary>
    public IAsyncRelayCommand ExportCommand { get; }

    /// <summary>
    /// 清空所有条目命令
    /// </summary>
    public IRelayCommand ClearAllCommand { get; }

    /// <summary>
    /// 刷新命令
    /// </summary>
    public IRelayCommand RefreshCommand { get; }

    #endregion

    #region 方法

    /// <summary>
    /// 添加术语条目
    /// </summary>
    private void AddEntry()
    {
        try
        {
            var entry = new TerminologyEntry
            {
                SourceTerm = NewSourceTerm.Trim(),
                TargetTerm = NewTargetTerm.Trim(),
                Category = NewCategory?.Trim() ?? string.Empty,
                Description = NewDescription?.Trim() ?? string.Empty,
                Priority = NewPriority,
                IsEnabled = true
            };

            _terminologyManager.AddTerminologyEntry(entry);
            LoadTerminologyEntries();
            ClearForm();

            SetStatus($"已添加术语: {entry.SourceTerm} -> {entry.TargetTerm}");
            _logger.LogInformation("添加术语条目: {SourceTerm} -> {TargetTerm}", entry.SourceTerm, entry.TargetTerm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加术语条目失败");
            SetError($"添加失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新术语条目
    /// </summary>
    private void UpdateEntry()
    {
        if (SelectedEntry == null) return;

        try
        {
            var entry = new TerminologyEntry
            {
                SourceTerm = NewSourceTerm.Trim(),
                TargetTerm = NewTargetTerm.Trim(),
                Category = NewCategory?.Trim() ?? string.Empty,
                Description = NewDescription?.Trim() ?? string.Empty,
                Priority = NewPriority,
                IsEnabled = SelectedEntry.IsEnabled,
                CreatedAt = SelectedEntry.CreatedAt
            };

            if (_terminologyManager.UpdateTerminologyEntry(entry))
            {
                LoadTerminologyEntries();
                ClearForm();
                SetStatus($"已更新术语: {entry.SourceTerm}");
                _logger.LogInformation("更新术语条目: {SourceTerm}", entry.SourceTerm);
            }
            else
            {
                SetError("更新失败: 未找到指定的术语条目");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新术语条目失败");
            SetError($"更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除术语条目
    /// </summary>
    private void DeleteEntry()
    {
        if (SelectedEntry == null) return;

        var result = MessageBox.Show(
            $"确定要删除术语 '{SelectedEntry.SourceTerm}' 吗？",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                if (_terminologyManager.RemoveTerminologyEntry(SelectedEntry.SourceTerm))
                {
                    LoadTerminologyEntries();
                    ClearForm();
                    SetStatus($"已删除术语: {SelectedEntry.SourceTerm}");
                    _logger.LogInformation("删除术语条目: {SourceTerm}", SelectedEntry.SourceTerm);
                }
                else
                {
                    SetError("删除失败: 未找到指定的术语条目");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除术语条目失败");
                SetError($"删除失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 清空表单
    /// </summary>
    private void ClearForm()
    {
        NewSourceTerm = string.Empty;
        NewTargetTerm = string.Empty;
        NewCategory = string.Empty;
        NewDescription = string.Empty;
        NewPriority = 5;
        IsEditMode = false;
        SelectedEntry = null;
    }

    /// <summary>
    /// 搜索术语条目
    /// </summary>
    private void SearchEntries()
    {
        FilteredEntries.Clear();

        var entries = string.IsNullOrWhiteSpace(SearchKeyword)
            ? TerminologyEntries
            : TerminologyEntries.Where(e =>
                e.SourceTerm.Contains(SearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                e.TargetTerm.Contains(SearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(e.Category) && e.Category.Contains(SearchKeyword, StringComparison.OrdinalIgnoreCase)));

        foreach (var entry in entries)
        {
            FilteredEntries.Add(entry);
        }
    }

    /// <summary>
    /// 导入术语库
    /// </summary>
    private async Task ImportTerminologyAsync()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "导入术语库",
            Filter = "术语库文件|*.md;*.csv;*.txt|JSON文件|*.json|CSV文件|*.csv|Markdown文件|*.md|文本文件|*.txt|Excel文件|*.xlsx|所有支持的文件|*.json;*.csv;*.xlsx;*.md;*.txt",
            CheckFileExists = true
        };

        if (openFileDialog.ShowDialog() == true)
        {
            try
            {
                IsBusy = true;
                SetStatus("正在导入术语库...");

                var extension = Path.GetExtension(openFileDialog.FileName).ToLowerInvariant();
                var format = extension switch
                {
                    ".json" => "json",
                    ".csv" => "csv",
                    ".md" => "md",
                    ".txt" => "txt",
                    ".xlsx" => "xlsx",
                    _ => "csv" // 默认使用CSV格式解析
                };

                await _terminologyManager.ImportTerminologyAsync(openFileDialog.FileName, format);
                LoadTerminologyEntries();

                SetStatus($"成功导入术语库，共 {TerminologyEntries.Count} 条术语");
                _logger.LogInformation("导入术语库成功: {FilePath}, 格式: {Format}", openFileDialog.FileName, format);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入术语库失败: {FilePath}", openFileDialog.FileName);
                SetError($"导入失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }

    /// <summary>
    /// 导出术语库
    /// </summary>
    private async Task ExportTerminologyAsync()
    {
        var saveFileDialog = new SaveFileDialog
        {
            Title = "导出术语库",
            Filter = "术语库文件|*.md|CSV文件|*.csv|JSON文件|*.json|文本文件|*.txt|Excel文件|*.xlsx",
            DefaultExt = "md",
            FileName = $"术语库_{DateTime.Now:yyyyMMdd_HHmmss}"
        };

        if (saveFileDialog.ShowDialog() == true)
        {
            try
            {
                IsBusy = true;
                SetStatus("正在导出术语库...");

                var extension = Path.GetExtension(saveFileDialog.FileName).ToLowerInvariant();
                var format = extension switch
                {
                    ".json" => "json",
                    ".csv" => "csv",
                    ".md" => "md",
                    ".txt" => "txt",
                    ".xlsx" => "xlsx",
                    _ => "md" // 默认使用Markdown格式
                };

                await _terminologyManager.ExportTerminologyAsync(saveFileDialog.FileName, format);

                SetStatus($"成功导出术语库到: {saveFileDialog.FileName}");
                _logger.LogInformation("导出术语库成功: {FilePath}, 格式: {Format}", saveFileDialog.FileName, format);

                // 询问是否打开文件夹
                var result = MessageBox.Show(
                    "导出成功！是否打开文件所在文件夹？",
                    "导出完成",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var directory = Path.GetDirectoryName(saveFileDialog.FileName);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = directory,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出术语库失败: {FilePath}", saveFileDialog.FileName);
                SetError($"导出失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }

    /// <summary>
    /// 清空所有术语条目
    /// </summary>
    private void ClearAllEntries()
    {
        var result = MessageBox.Show(
            "确定要清空所有术语条目吗？此操作不可撤销！",
            "确认清空",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                _terminologyManager.ClearTerminology();
                LoadTerminologyEntries();
                ClearForm();
                SetStatus("已清空所有术语条目");
                _logger.LogInformation("清空所有术语条目");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空术语条目失败");
                SetError($"清空失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 刷新术语条目
    /// </summary>
    private void RefreshEntries()
    {
        LoadTerminologyEntries();
        SetStatus("已刷新术语库");
    }

    /// <summary>
    /// 加载选中的条目到表单
    /// </summary>
    private void LoadSelectedEntry()
    {
        if (SelectedEntry != null)
        {
            NewSourceTerm = SelectedEntry.SourceTerm;
            NewTargetTerm = SelectedEntry.TargetTerm;
            NewCategory = SelectedEntry.Category;
            NewDescription = SelectedEntry.Description;
            NewPriority = SelectedEntry.Priority;
            IsEditMode = true;
        }
    }

    /// <summary>
    /// 加载术语条目
    /// </summary>
    private void LoadTerminologyEntries()
    {
        try
        {
            TerminologyEntries.Clear();
            var entries = _terminologyManager.GetAllEntries().OrderBy(e => e.SourceTerm);

            foreach (var entry in entries)
            {
                TerminologyEntries.Add(entry);
            }

            SearchEntries(); // 应用当前搜索过滤
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载术语条目失败");
            SetError($"加载术语库失败: {ex.Message}");
        }
    }

    #endregion
}
