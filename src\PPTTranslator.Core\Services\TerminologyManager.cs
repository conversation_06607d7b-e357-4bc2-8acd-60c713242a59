using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;
using OfficeOpenXml;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 术语库管理服务
/// </summary>
public class TerminologyManager : ITerminologyManager
{
    private readonly ILogger<TerminologyManager> _logger;
    private TerminologyDatabase _currentDatabase;

    public TerminologyManager(ILogger<TerminologyManager> logger)
    {
        _logger = logger;
        _currentDatabase = new TerminologyDatabase
        {
            Name = "默认术语库",
            SourceLanguage = "中文",
            TargetLanguage = "英文"
        };


    }

    public TerminologyDatabase CurrentDatabase => _currentDatabase;

    public async Task<TerminologyDatabase> LoadTerminologyAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("术语库文件不存在: {FilePath}", filePath);
                return new TerminologyDatabase();
            }

            var jsonContent = await File.ReadAllTextAsync(filePath);
            var database = JsonSerializer.Deserialize<TerminologyDatabase>(jsonContent);
            
            if (database != null)
            {
                _currentDatabase = database;
                _logger.LogInformation("成功加载术语库: {Name}, 条目数: {Count}", 
                    database.Name, database.Entries.Count);
            }

            return database ?? new TerminologyDatabase();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载术语库失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveTerminologyAsync(TerminologyDatabase database, string filePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            database.UpdatedAt = DateTime.Now;
            var jsonContent = JsonSerializer.Serialize(database, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(filePath, jsonContent);
            _logger.LogInformation("术语库保存成功: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存术语库失败: {FilePath}", filePath);
            throw;
        }
    }

    public void AddTerminologyEntry(TerminologyEntry entry)
    {
        if (string.IsNullOrWhiteSpace(entry.SourceTerm) || string.IsNullOrWhiteSpace(entry.TargetTerm))
        {
            throw new ArgumentException("源术语和目标术语不能为空");
        }

        // 检查是否已存在相同的源术语
        var existingEntry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(entry.SourceTerm, StringComparison.OrdinalIgnoreCase));

        if (existingEntry != null)
        {
            // 更新现有条目
            existingEntry.TargetTerm = entry.TargetTerm;
            existingEntry.Category = entry.Category;
            existingEntry.Description = entry.Description;
            existingEntry.Priority = entry.Priority;
            existingEntry.IsEnabled = entry.IsEnabled;
            existingEntry.UpdatedAt = DateTime.Now;
        }
        else
        {
            // 添加新条目
            entry.CreatedAt = DateTime.Now;
            entry.UpdatedAt = DateTime.Now;
            _currentDatabase.Entries.Add(entry);
        }

        _currentDatabase.UpdatedAt = DateTime.Now;
        _logger.LogInformation("添加术语条目: {SourceTerm} -> {TargetTerm}", entry.SourceTerm, entry.TargetTerm);
    }

    public bool RemoveTerminologyEntry(string sourceTerm)
    {
        var entry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(sourceTerm, StringComparison.OrdinalIgnoreCase));

        if (entry != null)
        {
            _currentDatabase.Entries.Remove(entry);
            _currentDatabase.UpdatedAt = DateTime.Now;
            _logger.LogInformation("删除术语条目: {SourceTerm}", sourceTerm);
            return true;
        }

        return false;
    }

    public bool UpdateTerminologyEntry(TerminologyEntry entry)
    {
        var existingEntry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(entry.SourceTerm, StringComparison.OrdinalIgnoreCase));

        if (existingEntry != null)
        {
            existingEntry.TargetTerm = entry.TargetTerm;
            existingEntry.Category = entry.Category;
            existingEntry.Description = entry.Description;
            existingEntry.Priority = entry.Priority;
            existingEntry.IsEnabled = entry.IsEnabled;
            existingEntry.UpdatedAt = DateTime.Now;
            _currentDatabase.UpdatedAt = DateTime.Now;
            
            _logger.LogInformation("更新术语条目: {SourceTerm}", entry.SourceTerm);
            return true;
        }

        return false;
    }

    public IEnumerable<TerminologyEntry> GetAllEntries()
    {
        return _currentDatabase.Entries.Where(e => e.IsEnabled).OrderByDescending(e => e.Priority);
    }

    public IEnumerable<TerminologyEntry> SearchEntries(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
        {
            return GetAllEntries();
        }

        return _currentDatabase.Entries.Where(e => 
            e.IsEnabled && 
            (e.SourceTerm.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.TargetTerm.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.Category.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.Description.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            .OrderByDescending(e => e.Priority);
    }

    public TerminologyPreprocessResult PreprocessText(string text, string sourceLanguage, string targetLanguage)
    {
        var result = new TerminologyPreprocessResult
        {
            OriginalText = text ?? string.Empty,
            ProcessedText = text ?? string.Empty
        };

        if (string.IsNullOrWhiteSpace(text))
        {
            result.ProcessedText = string.Empty;
            return result;
        }

        // 获取启用的术语条目，按长度降序排序（优先匹配长术语）
        var enabledEntries = _currentDatabase.Entries
            .Where(e => e.IsEnabled)
            .OrderByDescending(e => e.SourceTerm.Length)
            .ThenByDescending(e => e.Priority)
            .ToList();

        var processedText = text;
        var placeholderCounter = 0;

        foreach (var entry in enabledEntries)
        {
            // 对于中文等非拉丁语言，不使用单词边界
            var pattern = IsLatinText(entry.SourceTerm)
                ? $@"\b{Regex.Escape(entry.SourceTerm)}\b"
                : Regex.Escape(entry.SourceTerm);
            var matches = Regex.Matches(processedText, pattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                var placeholder = $"__TERM_{placeholderCounter++}__";
                
                var terminologyMatch = new TerminologyMatch
                {
                    Entry = entry,
                    StartIndex = match.Index,
                    Length = match.Length,
                    MatchedText = match.Value,
                    ReplacementText = entry.TargetTerm
                };

                result.Matches.Add(terminologyMatch);
                result.PlaceholderMap[placeholder] = entry.TargetTerm;

                // 替换为占位符
                processedText = processedText.Remove(match.Index, match.Length)
                    .Insert(match.Index, placeholder);
            }
        }

        result.ProcessedText = processedText;
        
        _logger.LogDebug("术语预处理完成，匹配到 {Count} 个术语", result.Matches.Count);
        return result;
    }

    public string PostprocessText(string translatedText, TerminologyPreprocessResult preprocessResult)
    {
        if (string.IsNullOrWhiteSpace(translatedText) || !preprocessResult.PlaceholderMap.Any())
        {
            return translatedText;
        }

        var result = translatedText;

        // 将占位符替换为目标术语
        foreach (var kvp in preprocessResult.PlaceholderMap)
        {
            result = result.Replace(kvp.Key, kvp.Value);
        }

        _logger.LogDebug("术语后处理完成，替换了 {Count} 个占位符", preprocessResult.PlaceholderMap.Count);
        return result;
    }

    public void ClearTerminology()
    {
        _currentDatabase.Entries.Clear();
        _currentDatabase.UpdatedAt = DateTime.Now;
        _logger.LogInformation("术语库已清空");
    }

    /// <summary>
    /// 判断文本是否为拉丁字符
    /// </summary>
    private bool IsLatinText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        // 检查是否主要包含拉丁字符
        var latinChars = text.Count(c => (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z'));
        return latinChars > text.Length * 0.5;
    }

    public async Task ImportTerminologyAsync(string filePath, string format)
    {
        // 实现不同格式的导入逻辑
        switch (format.ToLowerInvariant())
        {
            case "json":
                await ImportFromJsonAsync(filePath);
                break;
            case "csv":
            case "md":
            case "txt":
                await ImportFromCsvAsync(filePath); // CSV和Markdown使用相同的解析逻辑
                break;
            case "xlsx":
                await ImportFromExcelAsync(filePath);
                break;
            default:
                throw new NotSupportedException($"不支持的文件格式: {format}");
        }
    }

    public async Task ExportTerminologyAsync(string filePath, string format)
    {
        // 实现不同格式的导出逻辑
        switch (format.ToLowerInvariant())
        {
            case "json":
                await ExportToJsonAsync(filePath);
                break;
            case "csv":
            case "md":
            case "txt":
                await ExportToCsvAsync(filePath); // CSV和Markdown使用相同的格式
                break;
            case "xlsx":
                await ExportToExcelAsync(filePath);
                break;
            default:
                throw new NotSupportedException($"不支持的导出格式: {format}");
        }
    }

    private async Task ImportFromJsonAsync(string filePath)
    {
        var database = await LoadTerminologyAsync(filePath);
        foreach (var entry in database.Entries)
        {
            AddTerminologyEntry(entry);
        }
    }

    private async Task ImportFromCsvAsync(string filePath)
    {
        var lines = await File.ReadAllLinesAsync(filePath, System.Text.Encoding.UTF8);

        foreach (var line in lines)
        {
            // 跳过空行和注释行
            if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#") || line.StartsWith("导出时间") ||
                line.StartsWith("术语数量") || line.StartsWith("##"))
                continue;

            // 处理简单的逗号分隔格式：中文术语,英文术语
            var parts = line.Split(',');
            if (parts.Length >= 2)
            {
                var sourceTerm = parts[0].Trim().Trim('"');
                var targetTerm = parts[1].Trim().Trim('"');

                // 跳过标题行
                if (sourceTerm == "源术语" || sourceTerm == "中文术语" || string.IsNullOrWhiteSpace(sourceTerm))
                    continue;

                var entry = new TerminologyEntry
                {
                    SourceTerm = sourceTerm,
                    TargetTerm = targetTerm,
                    Category = parts.Length > 2 ? parts[2].Trim().Trim('"') : "通用",
                    Description = parts.Length > 3 ? parts[3].Trim().Trim('"') : "",
                    Priority = 5,
                    IsEnabled = true
                };

                AddTerminologyEntry(entry);
            }
        }
    }

    private async Task ImportFromExcelAsync(string filePath)
    {
        // 设置EPPlus许可证（忽略错误）
        try
        {
            // 尝试使用新的许可证设置方式
            var licenseProperty = typeof(ExcelPackage).GetProperty("License");
            if (licenseProperty != null)
            {
                // EPPlus 8.0+ 使用 License 属性
                licenseProperty.SetValue(null, Enum.Parse(licenseProperty.PropertyType, "NonCommercial"));
            }
            else
            {
                // 回退到旧的方式
                #pragma warning disable CS0618
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                #pragma warning restore CS0618
            }
        }
        catch
        {
            // 忽略许可证设置错误，继续执行
        }

        using var package = new ExcelPackage(new FileInfo(filePath));
        var worksheet = package.Workbook.Worksheets[0]; // 使用第一个工作表

        var rowCount = worksheet.Dimension?.Rows ?? 0;

        // 从第二行开始读取（跳过标题行）
        for (int row = 2; row <= rowCount; row++)
        {
            var sourceTerm = worksheet.Cells[row, 1].Text?.Trim();
            var targetTerm = worksheet.Cells[row, 2].Text?.Trim();

            if (!string.IsNullOrWhiteSpace(sourceTerm) && !string.IsNullOrWhiteSpace(targetTerm))
            {
                var entry = new TerminologyEntry
                {
                    SourceTerm = sourceTerm,
                    TargetTerm = targetTerm,
                    Category = worksheet.Cells[row, 3].Text?.Trim() ?? string.Empty,
                    Description = worksheet.Cells[row, 4].Text?.Trim() ?? string.Empty,
                    Priority = int.TryParse(worksheet.Cells[row, 5].Text, out var priority) ? priority : 5
                };

                AddTerminologyEntry(entry);
            }
        }

        await Task.CompletedTask; // 保持异步签名
    }

    private async Task ExportToJsonAsync(string filePath)
    {
        await SaveTerminologyAsync(_currentDatabase, filePath);
    }

    private async Task ExportToCsvAsync(string filePath)
    {
        var lines = new List<string>();

        // 添加文件头信息（按照示例术语库格式）
        lines.Add("# 术语库 - 中文 → 英文");
        lines.Add("");
        lines.Add($"导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        lines.Add($"术语数量: {_currentDatabase.Entries.Count}");
        lines.Add("");
        lines.Add("## 术语列表（逗号分隔，方便复制粘贴）");
        lines.Add("");

        // 添加术语数据（简单的逗号分隔格式）
        foreach (var entry in _currentDatabase.Entries.OrderBy(e => e.SourceTerm))
        {
            lines.Add($"{entry.SourceTerm},{entry.TargetTerm}");
        }

        await File.WriteAllLinesAsync(filePath, lines, System.Text.Encoding.UTF8);
    }

    private async Task ExportToExcelAsync(string filePath)
    {
        // 设置EPPlus许可证（忽略错误）
        try
        {
            // 尝试使用新的许可证设置方式
            var licenseProperty = typeof(ExcelPackage).GetProperty("License");
            if (licenseProperty != null)
            {
                // EPPlus 8.0+ 使用 License 属性
                licenseProperty.SetValue(null, Enum.Parse(licenseProperty.PropertyType, "NonCommercial"));
            }
            else
            {
                // 回退到旧的方式
                #pragma warning disable CS0618
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                #pragma warning restore CS0618
            }
        }
        catch
        {
            // 忽略许可证设置错误，继续执行
        }

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("术语库");

        // 设置标题行
        worksheet.Cells[1, 1].Value = "源术语";
        worksheet.Cells[1, 2].Value = "目标术语";
        worksheet.Cells[1, 3].Value = "类别";
        worksheet.Cells[1, 4].Value = "描述";
        worksheet.Cells[1, 5].Value = "优先级";
        worksheet.Cells[1, 6].Value = "启用";
        worksheet.Cells[1, 7].Value = "创建时间";
        worksheet.Cells[1, 8].Value = "更新时间";

        // 设置标题行样式
        using (var range = worksheet.Cells[1, 1, 1, 8])
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        // 填充数据
        var row = 2;
        foreach (var entry in _currentDatabase.Entries)
        {
            worksheet.Cells[row, 1].Value = entry.SourceTerm;
            worksheet.Cells[row, 2].Value = entry.TargetTerm;
            worksheet.Cells[row, 3].Value = entry.Category;
            worksheet.Cells[row, 4].Value = entry.Description;
            worksheet.Cells[row, 5].Value = entry.Priority;
            worksheet.Cells[row, 6].Value = entry.IsEnabled ? "是" : "否";
            worksheet.Cells[row, 7].Value = entry.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
            worksheet.Cells[row, 8].Value = entry.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss");
            row++;
        }

        // 自动调整列宽
        worksheet.Cells.AutoFitColumns();

        // 保存文件
        await package.SaveAsAsync(new FileInfo(filePath));
    }
}
