# PPT翻译工具 (C#版)

一个专业的PowerPoint翻译工具，支持中英双向翻译，具备智能术语库管理和字体自适应调整功能。

## 🌟 主要特性

### 核心功能
- **PPT文档翻译**: 支持 .pptx 和 .ppt 格式文件的翻译
- **多语种支持**: 支持中文、英文、日文、韩文等多种语言互译
- **智能术语库**: 专业术语预处理，确保翻译一致性和准确性
- **字体自适应**: 根据翻译后文本长度自动调整字体大小
- **实时进度**: 翻译过程可视化，支持进度跟踪

### 技术特点
- **纯C#实现**: 基于 .NET 8.0 和 WPF 技术栈
- **模块化架构**: 清晰的分层架构，易于扩展和维护
- **多翻译引擎**: 支持智谱AI、Ollama等多种翻译服务
- **高性能处理**: 异步处理，支持批量翻译
- **智能缓存**: 翻译结果缓存，提高效率

## 🏗️ 项目结构

```
PPTTranslator/
├── src/
│   ├── PPTTranslator.Core/          # 核心业务逻辑
│   │   ├── Models/                  # 数据模型
│   │   ├── Interfaces/              # 接口定义
│   │   ├── Services/                # 业务服务
│   │   └── Configuration/           # 配置管理
│   ├── PPTTranslator.API/           # 翻译API客户端
│   │   ├── Clients/                 # API客户端实现
│   │   └── Interfaces/              # API接口
│   └── PPTTranslator.UI/            # WPF用户界面
│       ├── Views/                   # 视图
│       ├── ViewModels/              # 视图模型
│       ├── Converters/              # 数据转换器
│       └── Styles/                  # 样式资源
├── tests/
│   └── PPTTranslator.Tests/         # 单元测试
├── Data/                            # 数据文件
│   └── terminology.json            # 默认术语库
└── appsettings.json                 # 应用配置
```

## 🚀 快速开始

### 环境要求
- .NET 8.0 SDK
- Visual Studio 2022 或 Visual Studio Code
- Windows 10/11 (WPF应用)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/PPTTranslator.git
cd PPTTranslator
```

2. **还原依赖**
```bash
dotnet restore
```

3. **配置翻译服务**

编辑 `appsettings.json` 文件，配置翻译服务：

```json
{
  "Translation": {
    "Provider": "ZhipuAI",
    "ApiKey": "your-api-key-here",
    "DefaultModel": "glm-4-flash"
  }
}
```

4. **构建项目**
```bash
dotnet build
```

5. **运行应用**
```bash
dotnet run --project src/PPTTranslator.UI
```

## 📖 使用指南

### 基本使用流程

1. **选择PPT文件**: 点击"选择PPT文件"按钮，选择要翻译的PowerPoint文件
2. **配置翻译选项**:
   - 选择源语言和目标语言
   - 选择翻译模型
   - 决定是否使用术语库
3. **开始翻译**: 点击"开始翻译"按钮，系统将自动处理
4. **查看结果**: 翻译完成后，可以查看翻译结果和打开输出文件

### 术语库管理

术语库功能可以确保专业术语的翻译一致性：

1. **添加术语**: 在术语库中添加专业术语对照
2. **术语预处理**: 翻译前自动识别和标记术语
3. **术语替换**: 翻译后将术语替换为标准译文

### 字体自适应

系统会根据翻译后文本的长度变化自动调整字体大小：

- **文本变长**: 适当缩小字体以适应原有空间
- **文本变短**: 适当放大字体保持视觉平衡
- **智能约束**: 字体大小调整在合理范围内

## ⚙️ 配置说明

### 翻译配置
```json
{
  "Translation": {
    "DefaultSourceLanguage": "zh-CN",
    "DefaultTargetLanguage": "en",
    "Provider": "ZhipuAI",
    "ApiKey": "your-api-key",
    "TimeoutSeconds": 30,
    "RetryCount": 3
  }
}
```

### 术语库配置
```json
{
  "Terminology": {
    "EnablePreprocessing": true,
    "MatchingMode": "Smart",
    "FuzzyMatchThreshold": 0.8,
    "CaseSensitive": false
  }
}
```

### 字体调整配置
```json
{
  "FontAdjustment": {
    "EnableAutoAdjustment": true,
    "MinFontSize": 8.0,
    "MaxFontSize": 72.0,
    "MaxScaleDown": 0.7,
    "MaxScaleUp": 1.2
  }
}
```

## 🔧 开发指南

### 添加新的翻译服务

1. 实现 `ITranslationClient` 接口
2. 在 `App.xaml.cs` 中注册服务
3. 更新配置文件支持新服务

### 扩展术语库功能

1. 继承 `ITerminologyManager` 接口
2. 实现自定义术语匹配算法
3. 添加新的导入/导出格式支持

### 自定义字体调整策略

1. 实现 `IFontAdjustmentService` 接口
2. 定义语言特定的调整规则
3. 集成机器学习模型（可选）

## 🧪 测试

运行单元测试：
```bash
dotnet test
```

运行特定测试类：
```bash
dotnet test --filter "ClassName=TerminologyManagerTests"
```

## 📦 部署

### 发布应用
```bash
dotnet publish src/PPTTranslator.UI -c Release -r win-x64 --self-contained
```

### 创建安装包
使用 WiX Toolset 或 Inno Setup 创建 Windows 安装包。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [DocumentFormat.OpenXml](https://github.com/OfficeDev/Open-XML-SDK) - PPT文档处理
- [MaterialDesignInXamlToolkit](https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit) - UI设计
- [CommunityToolkit.Mvvm](https://github.com/CommunityToolkit/dotnet) - MVVM框架

## 📞 支持

如有问题或建议，请：
- 创建 [Issue](https://github.com/your-repo/PPTTranslator/issues)
- 发送邮件至 <EMAIL>
- 查看 [Wiki](https://github.com/your-repo/PPTTranslator/wiki) 文档

---

**注意**: 使用前请确保已正确配置翻译服务API密钥，并遵守相关服务的使用条款。