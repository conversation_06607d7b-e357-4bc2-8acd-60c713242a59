using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// 术语库管理接口
/// </summary>
public interface ITerminologyManager
{
    /// <summary>
    /// 加载术语库
    /// </summary>
    /// <param name="filePath">术语库文件路径</param>
    /// <returns>加载的术语库</returns>
    Task<TerminologyDatabase> LoadTerminologyAsync(string filePath);

    /// <summary>
    /// 保存术语库
    /// </summary>
    /// <param name="database">术语库</param>
    /// <param name="filePath">保存路径</param>
    Task SaveTerminologyAsync(TerminologyDatabase database, string filePath);

    /// <summary>
    /// 添加术语条目
    /// </summary>
    /// <param name="entry">术语条目</param>
    void AddTerminologyEntry(TerminologyEntry entry);

    /// <summary>
    /// 删除术语条目
    /// </summary>
    /// <param name="sourceTerm">源术语</param>
    /// <returns>是否删除成功</returns>
    bool RemoveTerminologyEntry(string sourceTerm);

    /// <summary>
    /// 更新术语条目
    /// </summary>
    /// <param name="entry">术语条目</param>
    /// <returns>是否更新成功</returns>
    bool UpdateTerminologyEntry(TerminologyEntry entry);

    /// <summary>
    /// 获取所有术语条目
    /// </summary>
    /// <returns>术语条目列表</returns>
    IEnumerable<TerminologyEntry> GetAllEntries();

    /// <summary>
    /// 搜索术语条目
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <returns>匹配的术语条目</returns>
    IEnumerable<TerminologyEntry> SearchEntries(string keyword);

    /// <summary>
    /// 预处理文本（应用术语替换）
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="sourceLanguage">源语言</param>
    /// <param name="targetLanguage">目标语言</param>
    /// <returns>预处理结果</returns>
    TerminologyPreprocessResult PreprocessText(string text, string sourceLanguage, string targetLanguage);

    /// <summary>
    /// 后处理文本（恢复术语替换）
    /// </summary>
    /// <param name="translatedText">翻译后的文本</param>
    /// <param name="preprocessResult">预处理结果</param>
    /// <returns>最终文本</returns>
    string PostprocessText(string translatedText, TerminologyPreprocessResult preprocessResult);

    /// <summary>
    /// 清空术语库
    /// </summary>
    void ClearTerminology();

    /// <summary>
    /// 导入术语库
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">文件格式（json, csv, xlsx）</param>
    Task ImportTerminologyAsync(string filePath, string format);

    /// <summary>
    /// 导出术语库
    /// </summary>
    /// <param name="filePath">导出路径</param>
    /// <param name="format">导出格式（json, csv, xlsx）</param>
    Task ExportTerminologyAsync(string filePath, string format);

    /// <summary>
    /// 获取当前术语库
    /// </summary>
    TerminologyDatabase CurrentDatabase { get; }
}

/// <summary>
/// 术语预处理结果
/// </summary>
public class TerminologyPreprocessResult
{
    /// <summary>
    /// 预处理后的文本
    /// </summary>
    public string ProcessedText { get; set; } = string.Empty;

    /// <summary>
    /// 术语匹配列表
    /// </summary>
    public List<TerminologyMatch> Matches { get; set; } = new();

    /// <summary>
    /// 占位符映射（占位符 -> 目标术语）
    /// </summary>
    public Dictionary<string, string> PlaceholderMap { get; set; } = new();

    /// <summary>
    /// 原始文本
    /// </summary>
    public string OriginalText { get; set; } = string.Empty;
}
