# 更新日志

本文档记录了PPT翻译工具的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 完整的C#版本重构
- WPF用户界面
- 智能术语库管理系统
- 字体自适应调整功能
- 多翻译引擎支持（智谱AI、Ollama）
- 实时翻译进度跟踪
- 翻译结果缓存机制
- 完整的单元测试套件
- 自动化构建脚本

### 改进
- 模块化架构设计
- 异步处理提升性能
- 更好的错误处理和日志记录
- 用户友好的界面设计
- 配置管理系统

### 技术栈
- .NET 8.0
- WPF (Windows Presentation Foundation)
- DocumentFormat.OpenXml
- Material Design UI
- CommunityToolkit.Mvvm
- xUnit 测试框架
- Moq 模拟框架

## [1.0.0] - 2024-01-01 (Python版本)

### 新增
- 基本的PPT翻译功能
- 智谱AI翻译服务集成
- Ollama本地模型支持
- 简单的术语库管理
- Gradio Web界面
- 翻译进度跟踪
- Excel格式的翻译记录导出

### 功能特点
- 支持中英双向翻译
- PPT/PPTX文件格式支持
- 基础的术语库功能
- 实时翻译进度显示
- 翻译结果验证

### 技术实现
- Python 3.12+
- python-pptx库处理PPT文档
- Gradio构建Web界面
- pandas处理数据
- 智谱AI API集成

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增**: 新功能
- **改进**: 对现有功能的改进
- **修复**: 问题修复
- **移除**: 移除的功能
- **安全**: 安全相关的修复

### 迁移指南

#### 从Python版本迁移到C#版本

1. **配置文件迁移**
   - Python版本的 `data/config.json` → C#版本的 `appsettings.json`
   - 配置结构有所调整，请参考新的配置格式

2. **术语库迁移**
   - 术语库格式保持兼容
   - 可直接复制 `terminology.json` 文件

3. **功能对比**
   ```
   Python版本 → C#版本
   ├── Gradio Web界面 → WPF桌面应用
   ├── 基础术语库 → 智能术语库管理
   ├── 简单字体处理 → 字体自适应调整
   ├── 基础进度显示 → 实时进度跟踪
   └── Excel导出 → 内置结果查看
   ```

4. **性能提升**
   - 更快的PPT文档处理
   - 异步翻译处理
   - 智能缓存机制
   - 更好的内存管理

### 已知问题

#### C#版本
- 目前仅支持Windows平台（WPF限制）
- 需要.NET 8.0运行时
- 某些复杂PPT格式可能需要额外处理

#### Python版本（已停止维护）
- Web界面在某些浏览器中可能有兼容性问题
- 大文件处理性能有限
- 术语库功能相对简单

### 路线图

#### 计划中的功能
- [ ] 跨平台支持（Avalonia UI）
- [ ] 更多翻译服务集成
- [ ] 批量文件处理
- [ ] 翻译质量评估
- [ ] 自定义翻译模板
- [ ] 云端术语库同步
- [ ] 插件系统

#### 长期目标
- [ ] 机器学习驱动的字体调整
- [ ] 智能翻译建议
- [ ] 协作翻译功能
- [ ] 移动端应用

### 贡献者

感谢所有为项目做出贡献的开发者：

- **项目维护者**: [您的名字]
- **核心开发**: [团队成员]
- **测试**: [测试团队]
- **文档**: [文档团队]

### 支持

如果您在使用过程中遇到问题：

1. 查看 [README.md](README.md) 了解基本使用方法
2. 查看 [TESTING.md](TESTING.md) 了解测试相关信息
3. 在 [Issues](https://github.com/your-repo/issues) 中搜索已知问题
4. 创建新的 Issue 报告问题或建议

### 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
