# C# 警告检查脚本
# 用于检查和分析C#项目中的编译警告

param(
    [string]$SolutionPath = "PPTTranslator.sln",
    [switch]$Detailed = $false,
    [switch]$FixNullable = $false
)

Write-Host "🔍 开始C#代码警告检查..." -ForegroundColor Green

# 检查.NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ 检测到 .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到 .NET SDK" -ForegroundColor Red
    exit 1
}

# 构建项目并捕获警告
Write-Host "`n🔨 构建项目并分析警告..." -ForegroundColor Yellow

$buildOutput = dotnet build $SolutionPath --verbosity normal --no-restore 2>&1
$buildExitCode = $LASTEXITCODE

# 解析构建输出
$warnings = @()
$errors = @()

foreach ($line in $buildOutput) {
    if ($line -match "warning\s+(\w+):\s+(.+)\s+\[(.+)\]") {
        $warnings += @{
            Code = $matches[1]
            Message = $matches[2]
            Project = $matches[3]
            Line = $line
        }
    }
    elseif ($line -match "error\s+(\w+):\s+(.+)\s+\[(.+)\]") {
        $errors += @{
            Code = $matches[1]
            Message = $matches[2]
            Project = $matches[3]
            Line = $line
        }
    }
}

# 显示结果
Write-Host "`n📊 构建结果分析:" -ForegroundColor Cyan

if ($buildExitCode -eq 0) {
    Write-Host "✅ 构建成功" -ForegroundColor Green
} else {
    Write-Host "❌ 构建失败" -ForegroundColor Red
}

Write-Host "📈 统计信息:" -ForegroundColor White
Write-Host "  - 错误数量: $($errors.Count)" -ForegroundColor $(if ($errors.Count -eq 0) { "Green" } else { "Red" })
Write-Host "  - 警告数量: $($warnings.Count)" -ForegroundColor $(if ($warnings.Count -eq 0) { "Green" } else { "Yellow" })

# 详细显示错误
if ($errors.Count -gt 0) {
    Write-Host "`n❌ 编译错误:" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  [$($error.Code)] $($error.Message)" -ForegroundColor Red
        if ($Detailed) {
            Write-Host "    项目: $($error.Project)" -ForegroundColor Gray
        }
    }
}

# 详细显示警告
if ($warnings.Count -gt 0) {
    Write-Host "`n⚠️ 编译警告:" -ForegroundColor Yellow
    
    # 按警告类型分组
    $warningGroups = $warnings | Group-Object -Property Code
    
    foreach ($group in $warningGroups) {
        Write-Host "`n  警告类型: $($group.Name) (共 $($group.Count) 个)" -ForegroundColor Cyan
        
        switch ($group.Name) {
            "CS8601" { Write-Host "    说明: 可能的 null 引用赋值" -ForegroundColor Gray }
            "CS8603" { Write-Host "    说明: 可能返回 null 引用" -ForegroundColor Gray }
            "CS8604" { Write-Host "    说明: 可能的 null 引用参数" -ForegroundColor Gray }
            "CS8618" { Write-Host "    说明: 不可为 null 的字段必须包含非 null 值" -ForegroundColor Gray }
            "CS8625" { Write-Host "    说明: 无法将 null 字面量转换为不可为 null 的引用类型" -ForegroundColor Gray }
            default { Write-Host "    说明: 其他警告" -ForegroundColor Gray }
        }
        
        if ($Detailed) {
            foreach ($warning in $group.Group) {
                Write-Host "    - $($warning.Message)" -ForegroundColor Yellow
                Write-Host "      项目: $($warning.Project)" -ForegroundColor Gray
            }
        }
    }
}

# 运行测试
Write-Host "`n🧪 运行单元测试..." -ForegroundColor Yellow
$testOutput = dotnet test --verbosity normal --no-build 2>&1
$testExitCode = $LASTEXITCODE

if ($testExitCode -eq 0) {
    Write-Host "✅ 所有测试通过" -ForegroundColor Green
} else {
    Write-Host "❌ 测试失败" -ForegroundColor Red
    if ($Detailed) {
        Write-Host "测试输出:" -ForegroundColor Gray
        $testOutput | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    }
}

# 代码分析建议
Write-Host "`n💡 代码质量建议:" -ForegroundColor Cyan

if ($warnings.Count -eq 0 -and $errors.Count -eq 0) {
    Write-Host "🎉 代码质量优秀，没有发现警告或错误！" -ForegroundColor Green
} else {
    if ($errors.Count -gt 0) {
        Write-Host "🔧 请优先修复编译错误" -ForegroundColor Red
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "⚠️ 建议处理以下类型的警告:" -ForegroundColor Yellow
        
        $nullableWarnings = $warnings | Where-Object { $_.Code -match "CS860[1-9]|CS8618|CS8625" }
        if ($nullableWarnings.Count -gt 0) {
            Write-Host "  - Nullable引用类型警告 ($($nullableWarnings.Count) 个)" -ForegroundColor Yellow
            Write-Host "    建议: 添加null检查或使用null-forgiving操作符(!)" -ForegroundColor Gray
        }
        
        $otherWarnings = $warnings | Where-Object { $_.Code -notmatch "CS860[1-9]|CS8618|CS8625" }
        if ($otherWarnings.Count -gt 0) {
            Write-Host "  - 其他警告 ($($otherWarnings.Count) 个)" -ForegroundColor Yellow
            Write-Host "    建议: 根据具体警告类型进行修复" -ForegroundColor Gray
        }
    }
}

# 自动修复选项
if ($FixNullable -and $warnings.Count -gt 0) {
    Write-Host "`n🔧 尝试自动修复Nullable警告..." -ForegroundColor Yellow
    Write-Host "注意: 这是实验性功能，请在修复后仔细检查代码" -ForegroundColor Red
    
    # 这里可以添加自动修复逻辑
    # 目前只是提示，实际修复需要更复杂的代码分析
    Write-Host "自动修复功能正在开发中..." -ForegroundColor Gray
}

# 生成报告
$reportPath = "build-warnings-report.txt"
$report = @"
C# 代码警告检查报告
生成时间: $(Get-Date)
解决方案: $SolutionPath

构建状态: $(if ($buildExitCode -eq 0) { "成功" } else { "失败" })
测试状态: $(if ($testExitCode -eq 0) { "通过" } else { "失败" })

统计信息:
- 编译错误: $($errors.Count)
- 编译警告: $($warnings.Count)

详细警告:
$($warnings | ForEach-Object { "[$($_.Code)] $($_.Message)" } | Out-String)
"@

$report | Out-File -FilePath $reportPath -Encoding UTF8
Write-Host "`n📄 详细报告已保存到: $reportPath" -ForegroundColor Green

# 返回退出代码
if ($errors.Count -gt 0) {
    exit 1
} elseif ($warnings.Count -gt 0) {
    exit 2  # 有警告但无错误
} else {
    exit 0  # 完全正常
}
