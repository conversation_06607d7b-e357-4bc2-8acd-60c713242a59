using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// 翻译服务接口
/// </summary>
public interface ITranslationService
{
    /// <summary>
    /// 翻译文本
    /// </summary>
    /// <param name="request">翻译请求</param>
    /// <returns>翻译响应</returns>
    Task<TranslationResponse> TranslateAsync(TranslationRequest request);

    /// <summary>
    /// 批量翻译文本
    /// </summary>
    /// <param name="requests">翻译请求列表</param>
    /// <returns>翻译响应列表</returns>
    Task<List<TranslationResponse>> TranslateBatchAsync(List<TranslationRequest> requests);

    /// <summary>
    /// 检测文本语言
    /// </summary>
    /// <param name="text">文本</param>
    /// <returns>检测到的语言代码</returns>
    Task<string> DetectLanguageAsync(string text);

    /// <summary>
    /// 获取支持的语言列表
    /// </summary>
    /// <returns>支持的语言列表</returns>
    Task<List<SupportedLanguage>> GetSupportedLanguagesAsync();

    /// <summary>
    /// 验证翻译服务连接
    /// </summary>
    /// <returns>是否连接成功</returns>
    Task<bool> ValidateConnectionAsync();

    /// <summary>
    /// 获取翻译服务信息
    /// </summary>
    /// <returns>服务信息</returns>
    Task<TranslationServiceInfo> GetServiceInfoAsync();
}

/// <summary>
/// 支持的语言
/// </summary>
public class SupportedLanguage
{
    /// <summary>
    /// 语言代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 语言名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 本地化名称
    /// </summary>
    public string NativeName { get; set; } = string.Empty;

    /// <summary>
    /// 是否支持作为源语言
    /// </summary>
    public bool SupportedAsSource { get; set; } = true;

    /// <summary>
    /// 是否支持作为目标语言
    /// </summary>
    public bool SupportedAsTarget { get; set; } = true;
}

/// <summary>
/// 翻译服务信息
/// </summary>
public class TranslationServiceInfo
{
    /// <summary>
    /// 服务名称
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;

    /// <summary>
    /// 服务版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 服务提供商
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// 服务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 最大文本长度限制
    /// </summary>
    public int MaxTextLength { get; set; } = 5000;

    /// <summary>
    /// 每分钟请求限制
    /// </summary>
    public int RequestsPerMinute { get; set; } = 60;

    /// <summary>
    /// 支持的模型列表
    /// </summary>
    public List<string> SupportedModels { get; set; } = new();
}
