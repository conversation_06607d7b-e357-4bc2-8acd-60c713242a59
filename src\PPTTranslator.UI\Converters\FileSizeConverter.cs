using System.Globalization;
using System.Windows.Data;

namespace PPTTranslator.UI.Converters;

/// <summary>
/// 文件大小转换器
/// </summary>
public class FileSizeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is long fileSize)
        {
            return fileSize / 1024.0; // 转换为KB
        }
        return 0.0;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
