# PPT翻译工具构建脚本

param(
    [string]$Configuration = "Release",
    [string]$Runtime = "win-x64",
    [switch]$SelfContained = $true,
    [switch]$RunTests = $true,
    [switch]$CreatePackage = $false
)

Write-Host "开始构建 PPT翻译工具..." -ForegroundColor Green

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取项目根目录
$ProjectRoot = $PSScriptRoot
$SolutionFile = Join-Path $ProjectRoot "PPTTranslator.sln"
$UIProject = Join-Path $ProjectRoot "src\PPTTranslator.UI\PPTTranslator.UI.csproj"
$TestProject = Join-Path $ProjectRoot "tests\PPTTranslator.Tests\PPTTranslator.Tests.csproj"
$OutputDir = Join-Path $ProjectRoot "publish"

Write-Host "项目根目录: $ProjectRoot" -ForegroundColor Yellow
Write-Host "配置: $Configuration" -ForegroundColor Yellow
Write-Host "运行时: $Runtime" -ForegroundColor Yellow

# 清理输出目录
if (Test-Path $OutputDir) {
    Write-Host "清理输出目录..." -ForegroundColor Yellow
    Remove-Item $OutputDir -Recurse -Force
}

# 还原依赖
Write-Host "还原NuGet包..." -ForegroundColor Yellow
dotnet restore $SolutionFile

# 构建解决方案
Write-Host "构建解决方案..." -ForegroundColor Yellow
dotnet build $SolutionFile --configuration $Configuration --no-restore

# 运行测试
if ($RunTests) {
    Write-Host "运行单元测试..." -ForegroundColor Yellow
    dotnet test $TestProject --configuration $Configuration --no-build --verbosity normal
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "测试失败，构建中止"
        exit 1
    }
    
    Write-Host "所有测试通过!" -ForegroundColor Green
}

# 发布应用
Write-Host "发布应用程序..." -ForegroundColor Yellow

$PublishArgs = @(
    "publish"
    $UIProject
    "--configuration", $Configuration
    "--runtime", $Runtime
    "--output", $OutputDir
    "--no-build"
)

if ($SelfContained) {
    $PublishArgs += "--self-contained"
} else {
    $PublishArgs += "--no-self-contained"
}

& dotnet @PublishArgs

if ($LASTEXITCODE -ne 0) {
    Write-Error "发布失败"
    exit 1
}

# 复制配置文件和数据
Write-Host "复制配置文件和数据..." -ForegroundColor Yellow

$ConfigFiles = @(
    "appsettings.json",
    "Data\terminology.json"
)

foreach ($file in $ConfigFiles) {
    $sourcePath = Join-Path $ProjectRoot $file
    $destPath = Join-Path $OutputDir $file
    
    if (Test-Path $sourcePath) {
        $destDir = Split-Path $destPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item $sourcePath $destPath -Force
        Write-Host "已复制: $file" -ForegroundColor Gray
    }
}

# 创建启动脚本
Write-Host "创建启动脚本..." -ForegroundColor Yellow

$StartScript = @"
@echo off
echo 启动 PPT翻译工具...
echo.

REM 检查 .NET 运行时
if not exist "PPTTranslator.UI.exe" (
    echo 错误: 找不到应用程序文件
    pause
    exit /b 1
)

REM 启动应用程序
start "" "PPTTranslator.UI.exe"

REM 等待一下确保应用启动
timeout /t 2 /nobreak >nul

echo 应用程序已启动
echo 如果遇到问题，请检查配置文件 appsettings.json
echo.
pause
"@

$StartScript | Out-File -FilePath (Join-Path $OutputDir "启动.bat") -Encoding UTF8

# 创建说明文件
$ReadmeContent = @"
# PPT翻译工具

## 使用说明

1. 双击 "启动.bat" 或直接运行 "PPTTranslator.UI.exe" 启动应用程序
2. 首次使用前，请编辑 appsettings.json 配置翻译服务API密钥
3. 选择要翻译的PPT文件，配置翻译选项，然后开始翻译

## 配置文件

- appsettings.json: 主要配置文件
- Data\terminology.json: 术语库文件

## 系统要求

- Windows 10/11
- .NET 8.0 运行时 (如果是自包含版本则不需要)

## 技术支持

如有问题，请查看项目文档或联系技术支持。

构建时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
构建配置: $Configuration
运行时: $Runtime
"@

$ReadmeContent | Out-File -FilePath (Join-Path $OutputDir "使用说明.txt") -Encoding UTF8

# 创建安装包 (可选)
if ($CreatePackage) {
    Write-Host "创建安装包..." -ForegroundColor Yellow
    
    $PackageName = "PPTTranslator-$Configuration-$Runtime-$(Get-Date -Format 'yyyyMMdd').zip"
    $PackagePath = Join-Path $ProjectRoot $PackageName
    
    # 压缩发布文件
    Compress-Archive -Path "$OutputDir\*" -DestinationPath $PackagePath -Force
    
    Write-Host "安装包已创建: $PackageName" -ForegroundColor Green
}

Write-Host "" -ForegroundColor Green
Write-Host "构建完成!" -ForegroundColor Green
Write-Host "输出目录: $OutputDir" -ForegroundColor Green
Write-Host "" -ForegroundColor Green

# 显示输出文件信息
$OutputFiles = Get-ChildItem $OutputDir -Recurse | Where-Object { !$_.PSIsContainer }
$TotalSize = ($OutputFiles | Measure-Object -Property Length -Sum).Sum / 1MB

Write-Host "输出文件统计:" -ForegroundColor Yellow
Write-Host "  文件数量: $($OutputFiles.Count)" -ForegroundColor Gray
Write-Host "  总大小: $([math]::Round($TotalSize, 2)) MB" -ForegroundColor Gray

Write-Host "" -ForegroundColor Green
Write-Host "可以运行以下命令启动应用程序:" -ForegroundColor Yellow
Write-Host "  cd `"$OutputDir`"" -ForegroundColor Gray
Write-Host "  .\PPTTranslator.UI.exe" -ForegroundColor Gray
