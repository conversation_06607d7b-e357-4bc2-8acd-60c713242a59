using Microsoft.Extensions.Logging;
using Moq;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;
using PPTTranslator.Core.Services;
using Xunit;

namespace PPTTranslator.Tests.Services;

/// <summary>
/// 文本预处理器测试
/// </summary>
public class TextPreprocessorTests
{
    private readonly Mock<ITerminologyManager> _mockTerminologyManager;
    private readonly Mock<ILogger<TextPreprocessor>> _mockLogger;
    private readonly TextPreprocessor _textPreprocessor;

    public TextPreprocessorTests()
    {
        _mockTerminologyManager = new Mock<ITerminologyManager>();
        _mockLogger = new Mock<ILogger<TextPreprocessor>>();
        _textPreprocessor = new TextPreprocessor(_mockTerminologyManager.Object, _mockLogger.Object);
    }

    [Theory]
    [InlineData("Hello World", "en")]
    [InlineData("你好世界", "zh-CN")]
    [InlineData("こんにちは", "ja")]
    [InlineData("안녕하세요", "ko")]
    [InlineData("Привет мир", "ru")]
    public void DetectLanguage_ShouldReturnCorrectLanguage(string text, string expectedLanguage)
    {
        // Act
        var result = _textPreprocessor.DetectLanguage(text);

        // Assert
        Assert.Equal(expectedLanguage, result);
    }

    [Fact]
    public void CleanText_ShouldRemoveExtraWhitespace()
    {
        // Arrange
        var text = "  这是   一个  测试   文本  ";

        // Act
        var result = _textPreprocessor.CleanText(text);

        // Assert
        Assert.Equal("这是 一个 测试 文本", result);
    }

    [Fact]
    public void SplitLongText_ShouldSplitTextCorrectly()
    {
        // Arrange
        var longText = string.Join(" ", Enumerable.Repeat("这是一个测试句子。", 100));

        // Act
        var segments = _textPreprocessor.SplitLongText(longText, 100).ToList();

        // Assert
        Assert.True(segments.Count > 1);
        Assert.All(segments, segment => Assert.True(segment.Length <= 100 || segment.Split(' ').Length == 1));
    }

    [Fact]
    public void PreprocessText_WithTerminology_ShouldCallTerminologyManager()
    {
        // Arrange
        var text = "测试文本";
        var mockResult = new TerminologyPreprocessResult
        {
            OriginalText = text,
            ProcessedText = "processed text"
        };
        
        _mockTerminologyManager
            .Setup(x => x.PreprocessText(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .Returns(mockResult);

        // Act
        var result = _textPreprocessor.PreprocessText(text, "zh-CN", "en", true);

        // Assert
        _mockTerminologyManager.Verify(
            x => x.PreprocessText(It.IsAny<string>(), "zh-CN", "en"), 
            Times.Once);
        Assert.Equal("processed text", result.ProcessedText);
    }

    [Fact]
    public void PreprocessText_WithoutTerminology_ShouldNotCallTerminologyManager()
    {
        // Arrange
        var text = "测试文本";

        // Act
        var result = _textPreprocessor.PreprocessText(text, "zh-CN", "en", false);

        // Assert
        _mockTerminologyManager.Verify(
            x => x.PreprocessText(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), 
            Times.Never);
        Assert.NotNull(result);
    }

    [Fact]
    public void PostprocessText_ShouldCallTerminologyManager()
    {
        // Arrange
        var translatedText = "translated text";
        var preprocessResult = new TextPreprocessResult
        {
            TerminologyResult = new TerminologyPreprocessResult()
        };

        _mockTerminologyManager
            .Setup(x => x.PostprocessText(It.IsAny<string>(), It.IsAny<TerminologyPreprocessResult>()))
            .Returns("final text");

        // Act
        var result = _textPreprocessor.PostprocessText(translatedText, preprocessResult);

        // Assert
        _mockTerminologyManager.Verify(
            x => x.PostprocessText(translatedText, preprocessResult.TerminologyResult), 
            Times.Once);
        Assert.Equal("final text", result);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("   ")]
    public void PreprocessText_WithEmptyText_ShouldReturnEmptyResult(string text)
    {
        // Act
        var result = _textPreprocessor.PreprocessText(text, "zh-CN", "en");

        // Assert
        Assert.Equal(string.Empty, result.ProcessedText);
        Assert.Equal(text ?? string.Empty, result.OriginalText);
    }

    [Fact]
    public void SplitLongText_WithShortText_ShouldReturnSingleSegment()
    {
        // Arrange
        var shortText = "短文本";

        // Act
        var segments = _textPreprocessor.SplitLongText(shortText, 1000).ToList();

        // Assert
        Assert.Single(segments);
        Assert.Equal(shortText, segments[0]);
    }

    [Theory]
    [InlineData("这是中文文本，包含标点符号。", "这是中文文本，包含标点符号。")]
    [InlineData("This is English text, with punctuation.", "This is English text, with punctuation.")]
    [InlineData("Mixed 中英文 text.", "Mixed 中英文 text.")]
    public void CleanText_WithPunctuation_ShouldPreservePunctuation(string input, string expected)
    {
        // Act
        var result = _textPreprocessor.CleanText(input);

        // Assert
        Assert.Equal(expected, result);
    }
}
