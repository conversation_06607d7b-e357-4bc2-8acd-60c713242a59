namespace PPTTranslator.Core.Models;

/// <summary>
/// 翻译请求模型
/// </summary>
public class TranslationRequest
{
    /// <summary>
    /// 待翻译文本
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 源语言
    /// </summary>
    public string SourceLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 目标语言
    /// </summary>
    public string TargetLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 是否使用术语库
    /// </summary>
    public bool UseTerminology { get; set; } = true;

    /// <summary>
    /// 翻译上下文
    /// </summary>
    public string Context { get; set; } = string.Empty;

    /// <summary>
    /// 翻译模型类型
    /// </summary>
    public string ModelType { get; set; } = string.Empty;
}

/// <summary>
/// 翻译响应模型
/// </summary>
public class TranslationResponse
{
    /// <summary>
    /// 翻译结果
    /// </summary>
    public string TranslatedText { get; set; } = string.Empty;

    /// <summary>
    /// 原始文本
    /// </summary>
    public string OriginalText { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 使用的术语匹配
    /// </summary>
    public List<TerminologyMatch> TerminologyMatches { get; set; } = new();

    /// <summary>
    /// 翻译耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 翻译模型
    /// </summary>
    public string Model { get; set; } = string.Empty;
}

/// <summary>
/// PPT文本元素模型
/// </summary>
public class PPTTextElement
{
    /// <summary>
    /// 元素ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 页面索引
    /// </summary>
    public int SlideIndex { get; set; }

    /// <summary>
    /// 形状索引
    /// </summary>
    public int ShapeIndex { get; set; }

    /// <summary>
    /// 文本内容
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 原始字体大小
    /// </summary>
    public double OriginalFontSize { get; set; }

    /// <summary>
    /// 调整后字体大小
    /// </summary>
    public double AdjustedFontSize { get; set; }

    /// <summary>
    /// 字体名称
    /// </summary>
    public string FontName { get; set; } = string.Empty;

    /// <summary>
    /// 文本位置信息
    /// </summary>
    public string Position { get; set; } = string.Empty;

    /// <summary>
    /// 文本格式信息
    /// </summary>
    public string Format { get; set; } = string.Empty;

    /// <summary>
    /// 翻译结果
    /// </summary>
    public string TranslatedText { get; set; } = string.Empty;

    /// <summary>
    /// 是否已翻译
    /// </summary>
    public bool IsTranslated { get; set; }

    /// <summary>
    /// 翻译时间
    /// </summary>
    public DateTime TranslatedAt { get; set; }
}

/// <summary>
/// 翻译进度信息
/// </summary>
public class TranslationProgress
{
    /// <summary>
    /// 当前处理的页面
    /// </summary>
    public int CurrentSlide { get; set; }

    /// <summary>
    /// 总页面数
    /// </summary>
    public int TotalSlides { get; set; }

    /// <summary>
    /// 当前处理的形状
    /// </summary>
    public int CurrentShape { get; set; }

    /// <summary>
    /// 当前页面形状总数
    /// </summary>
    public int TotalShapes { get; set; }

    /// <summary>
    /// 当前处理的文本
    /// </summary>
    public string CurrentText { get; set; } = string.Empty;

    /// <summary>
    /// 翻译结果
    /// </summary>
    public string TranslatedText { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public double ProgressPercentage { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage { get; set; } = string.Empty;

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// 是否出错
    /// </summary>
    public bool HasError { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
}
