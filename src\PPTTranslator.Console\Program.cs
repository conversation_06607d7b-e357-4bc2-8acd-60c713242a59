using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PPTTranslator.API.Clients;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Services;

namespace PPTTranslator.Console;

class Program
{
    static async Task Main(string[] args)
    {
        System.Console.WriteLine("=================================");
        System.Console.WriteLine("PPT翻译工具 - 控制台版本");
        System.Console.WriteLine("=================================");
        System.Console.WriteLine();

        // 创建主机
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices(ConfigureServices)
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            })
            .Build();

        try
        {
            // 获取服务
            var terminologyManager = host.Services.GetRequiredService<ITerminologyManager>();
            var textPreprocessor = host.Services.GetRequiredService<ITextPreprocessor>();
            var fontAdjustmentService = host.Services.GetRequiredService<IFontAdjustmentService>();

            System.Console.WriteLine("✓ 服务初始化完成");

            // 测试术语库功能
            await TestTerminologyManager(terminologyManager);

            // 测试文本预处理
            TestTextPreprocessor(textPreprocessor);

            // 测试字体调整
            TestFontAdjustment(fontAdjustmentService);

            System.Console.WriteLine();
            System.Console.WriteLine("=================================");
            System.Console.WriteLine("所有测试完成！");
            System.Console.WriteLine("=================================");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ 错误: {ex.Message}");
            System.Console.WriteLine($"详细信息: {ex}");
        }

        System.Console.WriteLine();
        System.Console.WriteLine("按任意键退出...");
        System.Console.ReadKey();
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // 配置服务
        services.AddSingleton<ConfigurationService>();

        // 核心服务
        services.AddSingleton<ITerminologyManager, TerminologyManager>();
        services.AddSingleton<ITextPreprocessor, TextPreprocessor>();
        services.AddSingleton<IFontAdjustmentService, FontAdjustmentService>();

        // HTTP客户端
        services.AddHttpClient<ZhipuAIClient>();
        services.AddHttpClient<OllamaClient>();

        // 翻译客户端配置
        services.AddSingleton<TranslationClientConfig>(provider =>
        {
            return new TranslationClientConfig
            {
                ApiKey = "",
                ServerUrl = "http://localhost:11434",
                TimeoutSeconds = 30,
                RetryCount = 3,
                DefaultModel = "glm-4-flash"
            };
        });
    }

    private static async Task TestTerminologyManager(ITerminologyManager terminologyManager)
    {
        System.Console.WriteLine("📚 测试术语库管理...");

        // 添加测试术语
        terminologyManager.AddTerminologyEntry(new PPTTranslator.Core.Models.TerminologyEntry
        {
            SourceTerm = "人工智能",
            TargetTerm = "Artificial Intelligence",
            Category = "技术",
            Description = "AI技术"
        });

        terminologyManager.AddTerminologyEntry(new PPTTranslator.Core.Models.TerminologyEntry
        {
            SourceTerm = "机器学习",
            TargetTerm = "Machine Learning",
            Category = "技术",
            Description = "ML技术"
        });

        System.Console.WriteLine("✓ 添加了2个测试术语");

        // 测试术语预处理
        var testText = "人工智能和机器学习是现代技术的重要组成部分";
        var preprocessResult = terminologyManager.PreprocessText(testText, "zh-CN", "en");

        System.Console.WriteLine($"原文: {testText}");
        System.Console.WriteLine($"预处理后: {preprocessResult.ProcessedText}");
        System.Console.WriteLine($"匹配术语数: {preprocessResult.Matches.Count}");

        foreach (var match in preprocessResult.Matches)
        {
            System.Console.WriteLine($"  - {match.MatchedText} -> {match.ReplacementText}");
        }

        // 测试后处理
        var mockTranslatedText = "Artificial Intelligence and Machine Learning are important components of modern technology";
        var finalText = terminologyManager.PostprocessText(mockTranslatedText, preprocessResult);
        System.Console.WriteLine($"最终结果: {finalText}");

        System.Console.WriteLine("✓ 术语库测试完成");
        System.Console.WriteLine();
    }

    private static void TestTextPreprocessor(ITextPreprocessor textPreprocessor)
    {
        System.Console.WriteLine("🔤 测试文本预处理...");

        // 测试语言检测
        var chineseText = "这是中文文本";
        var englishText = "This is English text";
        var mixedText = "这是 mixed 文本";

        System.Console.WriteLine($"'{chineseText}' -> {textPreprocessor.DetectLanguage(chineseText)}");
        System.Console.WriteLine($"'{englishText}' -> {textPreprocessor.DetectLanguage(englishText)}");
        System.Console.WriteLine($"'{mixedText}' -> {textPreprocessor.DetectLanguage(mixedText)}");

        // 测试文本清理
        var dirtyText = "  这是   一个  有很多   空格的   文本  ";
        var cleanedText = textPreprocessor.CleanText(dirtyText);
        System.Console.WriteLine($"清理前: '{dirtyText}'");
        System.Console.WriteLine($"清理后: '{cleanedText}'");

        // 测试长文本分割
        var longText = string.Join(" ", Enumerable.Repeat("这是一个测试句子。", 50));
        var segments = textPreprocessor.SplitLongText(longText, 100).ToList();
        System.Console.WriteLine($"长文本分割: {longText.Length} 字符 -> {segments.Count} 段");

        System.Console.WriteLine("✓ 文本预处理测试完成");
        System.Console.WriteLine();
    }

    private static void TestFontAdjustment(IFontAdjustmentService fontAdjustmentService)
    {
        System.Console.WriteLine("🔤 测试字体调整...");

        // 测试字体大小计算
        var originalText = "短文本";
        var translatedText = "This is a much longer translated text";
        var originalFontSize = 12.0;

        var adjustedSize = fontAdjustmentService.CalculateAdjustedFontSize(
            originalText, translatedText, originalFontSize);

        System.Console.WriteLine($"原文: '{originalText}' (长度: {originalText.Length})");
        System.Console.WriteLine($"译文: '{translatedText}' (长度: {translatedText.Length})");
        System.Console.WriteLine($"字体调整: {originalFontSize} -> {adjustedSize:F1}");

        // 测试文本尺寸估算
        var textSize = fontAdjustmentService.EstimateTextSize(translatedText, adjustedSize, "Arial");
        System.Console.WriteLine($"估算尺寸: {textSize.Width:F1} x {textSize.Height:F1}");

        // 测试调整策略
        var strategy = fontAdjustmentService.GetAdjustmentStrategy("zh-CN", "en");
        System.Console.WriteLine($"调整策略: 最小{strategy.MinFontSize}, 最大{strategy.MaxFontSize}, 缩放范围{strategy.MaxScaleDown}-{strategy.MaxScaleUp}");

        System.Console.WriteLine("✓ 字体调整测试完成");
        System.Console.WriteLine();
    }
}
