<Window x:Class="PPTTranslator.SimpleUI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="PPT翻译工具 - 简化版" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="PPT翻译工具" FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20" Foreground="DarkBlue"/>

        <!-- 文件选择 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="PPT文件:" VerticalAlignment="Center" Width="80"/>
            <TextBox x:Name="FilePathTextBox" Width="500" Margin="5,0"/>
            <Button x:Name="SelectFileButton" Content="选择文件" Width="100" Margin="5,0" Click="SelectFileButton_Click"/>
        </StackPanel>

        <!-- 翻译选项 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="翻译方向:" VerticalAlignment="Center" Width="80"/>
            <ComboBox x:Name="LanguageComboBox" Width="150" Margin="5,0" SelectedIndex="0">
                <ComboBoxItem Content="中文 → 英文"/>
                <ComboBoxItem Content="英文 → 中文"/>
            </ComboBox>
            <CheckBox x:Name="UseTerminologyCheckBox" Content="使用术语库" IsChecked="True" 
                      VerticalAlignment="Center" Margin="20,0"/>
            <Button x:Name="TranslateButton" Content="开始翻译" Width="100" Margin="20,0" 
                    Click="TranslateButton_Click" IsEnabled="False"/>
        </StackPanel>

        <!-- 结果显示 -->
        <ScrollViewer Grid.Row="3" VerticalScrollBarVisibility="Auto">
            <TextBox x:Name="ResultTextBox" TextWrapping="Wrap" AcceptsReturn="True" 
                     IsReadOnly="True" Background="LightGray"/>
        </ScrollViewer>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="4" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="StatusTextBlock" Text="就绪"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
