using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// 文本预处理接口
/// </summary>
public interface ITextPreprocessor
{
    /// <summary>
    /// 预处理文本
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="sourceLanguage">源语言</param>
    /// <param name="targetLanguage">目标语言</param>
    /// <param name="useTerminology">是否使用术语库</param>
    /// <returns>预处理结果</returns>
    TextPreprocessResult PreprocessText(string text, string sourceLanguage, string targetLanguage, bool useTerminology = true);

    /// <summary>
    /// 后处理文本
    /// </summary>
    /// <param name="translatedText">翻译后的文本</param>
    /// <param name="preprocessResult">预处理结果</param>
    /// <returns>最终处理后的文本</returns>
    string PostprocessText(string translatedText, TextPreprocessResult preprocessResult);

    /// <summary>
    /// 清理文本（移除多余空格、特殊字符等）
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>清理后的文本</returns>
    string CleanText(string text);

    /// <summary>
    /// 检测文本语言
    /// </summary>
    /// <param name="text">文本</param>
    /// <returns>检测到的语言</returns>
    string DetectLanguage(string text);

    /// <summary>
    /// 分割长文本
    /// </summary>
    /// <param name="text">长文本</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>分割后的文本片段</returns>
    IEnumerable<string> SplitLongText(string text, int maxLength = 1000);
}

/// <summary>
/// 文本预处理结果
/// </summary>
public class TextPreprocessResult
{
    /// <summary>
    /// 原始文本
    /// </summary>
    public string OriginalText { get; set; } = string.Empty;

    /// <summary>
    /// 预处理后的文本
    /// </summary>
    public string ProcessedText { get; set; } = string.Empty;

    /// <summary>
    /// 术语预处理结果
    /// </summary>
    public TerminologyPreprocessResult? TerminologyResult { get; set; }

    /// <summary>
    /// 检测到的语言
    /// </summary>
    public string DetectedLanguage { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要分割处理
    /// </summary>
    public bool RequiresSplitting { get; set; }

    /// <summary>
    /// 分割的文本片段
    /// </summary>
    public List<string> TextSegments { get; set; } = new();

    /// <summary>
    /// 预处理时间戳
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 预处理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
}
