"""warning_suppressor模块"""
"""
警告抑制器
用于抑制运行时的第三方库警告
"""

import warnings
import sys
from functools import wraps
from typing import Callable, Any


def suppress_third_party_warnings():
    """抑制第三方库的警告"""
    
    # 抑制datetime的弃用警告（来自第三方库）
    warnings.filterwarnings("ignore", 
                          message="datetime.datetime.utcfromtimestamp.*is deprecated",
                          category=DeprecationWarning)
    
    # 抑制importlib-resources的弃用警告
    warnings.filterwarnings("ignore", 
                          message="path is deprecated.*Use files.*instead",
                          category=DeprecationWarning)
    
    # 抑制gradio相关警告
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="gradio")
    warnings.filterwarnings("ignore", category=FutureWarning, module="gradio")
    
    # 抑制pandas相关警告
    warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="pandas")
    
    # 抑制pptx相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="pptx")
    
    # 抑制其他常见的第三方库警告
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="pkg_resources")
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="setuptools")
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="distutils")


def with_warning_suppression(func: Callable) -> Callable:
    """装饰器：在函数执行时抑制警告"""
    
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        # 保存当前警告设置
        old_filters = warnings.filters[:]
        
        try:
            # 应用警告抑制
            suppress_third_party_warnings()
            
            # 执行函数
            return func(*args, **kwargs)
            
        finally:
            # 恢复原始警告设置
            warnings.filters[:] = old_filters
    
    return wrapper


class WarningContext:
    """警告上下文管理器"""
    
    def __init__(self, suppress_all: bool = False):
        self.suppress_all = suppress_all
        self.old_filters = None
    
    def __enter__(self):
        # 保存当前警告设置
        self.old_filters = warnings.filters[:]
        
        if self.suppress_all:
            # 抑制所有警告
            warnings.filterwarnings("ignore")
        else:
            # 只抑制第三方库警告
            suppress_third_party_warnings()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始警告设置
        if self.old_filters is not None:
            warnings.filters[:] = self.old_filters


def setup_global_warning_filters():
    """设置全局警告过滤器"""
    
    # 在程序启动时调用，设置全局警告过滤
    suppress_third_party_warnings()
    
    # 设置警告显示格式
    def custom_warning_format(message, category, filename, lineno, file=None, line=None):
        """自定义警告格式"""
        return f"⚠️ {category.__name__}: {message}\n   文件: {filename}:{lineno}\n"
    
    # 应用自定义格式
    warnings.formatwarning = custom_warning_format


def check_and_report_warnings():
    """检查并报告当前的警告设置"""
    
    print("📋 当前警告过滤器设置:")
    print(f"   总共 {len(warnings.filters)} 个过滤器")
    
    for i, filter_item in enumerate(warnings.filters):
        action, message, category, module, lineno = filter_item
        print(f"   {i+1}. {action}: {category.__name__ if category else 'All'}")
        if module:
            print(f"      模块: {module}")
        if message:
            print(f"      消息: {message}")


# 在模块导入时自动设置警告过滤器
if __name__ != "__main__":
    setup_global_warning_filters()


if __name__ == "__main__":
    # 测试警告抑制功能
    print("🧪 测试警告抑制功能")
    
    # 显示当前设置
    check_and_report_warnings()
    
    # 测试上下文管理器
    print("\n🔧 测试上下文管理器...")
    with WarningContext():
        # 这里的警告应该被抑制
        warnings.warn("这是一个测试警告", DeprecationWarning)
    
    # 测试装饰器
    print("\n🎯 测试装饰器...")
    
    @with_warning_suppression
    def test_function():
        warnings.warn("这是另一个测试警告", DeprecationWarning)
        return "函数执行完成"
    
    result = test_function()
    print(f"结果: {result}")
    
    print("\n✅ 警告抑制功能测试完成")
