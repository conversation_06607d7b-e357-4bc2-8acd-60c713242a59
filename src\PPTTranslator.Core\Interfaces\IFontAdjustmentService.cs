using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Interfaces;

/// <summary>
/// 字体调整服务接口
/// </summary>
public interface IFontAdjustmentService
{
    /// <summary>
    /// 计算调整后的字体大小
    /// </summary>
    /// <param name="originalText">原始文本</param>
    /// <param name="translatedText">翻译后文本</param>
    /// <param name="originalFontSize">原始字体大小</param>
    /// <param name="maxWidth">最大宽度（可选）</param>
    /// <param name="maxHeight">最大高度（可选）</param>
    /// <returns>调整后的字体大小</returns>
    double CalculateAdjustedFontSize(
        string originalText, 
        string translatedText, 
        double originalFontSize, 
        double? maxWidth = null, 
        double? maxHeight = null);

    /// <summary>
    /// 批量计算字体调整
    /// </summary>
    /// <param name="textElements">文本元素列表</param>
    /// <returns>调整结果</returns>
    List<FontAdjustmentResult> CalculateBatchAdjustments(List<PPTTextElement> textElements);

    /// <summary>
    /// 估算文本渲染尺寸
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="fontSize">字体大小</param>
    /// <param name="fontName">字体名称</param>
    /// <returns>文本尺寸</returns>
    TextSize EstimateTextSize(string text, double fontSize, string fontName);

    /// <summary>
    /// 获取字体调整策略
    /// </summary>
    /// <param name="sourceLanguage">源语言</param>
    /// <param name="targetLanguage">目标语言</param>
    /// <returns>调整策略</returns>
    FontAdjustmentStrategy GetAdjustmentStrategy(string sourceLanguage, string targetLanguage);

    /// <summary>
    /// 验证字体调整结果
    /// </summary>
    /// <param name="adjustment">调整结果</param>
    /// <returns>验证结果</returns>
    FontAdjustmentValidation ValidateAdjustment(FontAdjustmentResult adjustment);
}

/// <summary>
/// 字体调整结果
/// </summary>
public class FontAdjustmentResult
{
    /// <summary>
    /// 文本元素ID
    /// </summary>
    public string ElementId { get; set; } = string.Empty;

    /// <summary>
    /// 原始字体大小
    /// </summary>
    public double OriginalFontSize { get; set; }

    /// <summary>
    /// 调整后字体大小
    /// </summary>
    public double AdjustedFontSize { get; set; }

    /// <summary>
    /// 调整比例
    /// </summary>
    public double AdjustmentRatio { get; set; }

    /// <summary>
    /// 调整原因
    /// </summary>
    public string AdjustmentReason { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要调整
    /// </summary>
    public bool RequiresAdjustment { get; set; }

    /// <summary>
    /// 原始文本尺寸
    /// </summary>
    public TextSize OriginalTextSize { get; set; } = new();

    /// <summary>
    /// 调整后文本尺寸
    /// </summary>
    public TextSize AdjustedTextSize { get; set; } = new();

    /// <summary>
    /// 置信度（0-1）
    /// </summary>
    public double Confidence { get; set; } = 1.0;
}

/// <summary>
/// 文本尺寸
/// </summary>
public class TextSize
{
    /// <summary>
    /// 宽度
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 高度
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 行数
    /// </summary>
    public int LineCount { get; set; } = 1;

    /// <summary>
    /// 字符数
    /// </summary>
    public int CharacterCount { get; set; }
}

/// <summary>
/// 字体调整策略
/// </summary>
public class FontAdjustmentStrategy
{
    /// <summary>
    /// 最小字体大小
    /// </summary>
    public double MinFontSize { get; set; } = 8.0;

    /// <summary>
    /// 最大字体大小
    /// </summary>
    public double MaxFontSize { get; set; } = 72.0;

    /// <summary>
    /// 最大缩放比例
    /// </summary>
    public double MaxScaleDown { get; set; } = 0.7;

    /// <summary>
    /// 最大放大比例
    /// </summary>
    public double MaxScaleUp { get; set; } = 1.2;

    /// <summary>
    /// 文本长度变化阈值
    /// </summary>
    public double LengthChangeThreshold { get; set; } = 0.2;

    /// <summary>
    /// 是否考虑字符宽度差异
    /// </summary>
    public bool ConsiderCharacterWidth { get; set; } = true;

    /// <summary>
    /// 语言特定的调整因子
    /// </summary>
    public double LanguageAdjustmentFactor { get; set; } = 1.0;

    /// <summary>
    /// 是否启用智能换行
    /// </summary>
    public bool EnableSmartWrapping { get; set; } = true;
}

/// <summary>
/// 字体调整验证结果
/// </summary>
public class FontAdjustmentValidation
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 建议信息
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// 可读性评分（0-10）
    /// </summary>
    public double ReadabilityScore { get; set; } = 10.0;
}
